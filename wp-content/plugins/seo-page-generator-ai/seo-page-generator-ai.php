<?php
/**
 * Plugin Name: SEO Page Generator AI
 * Plugin URI: https://ukdebtadvisor.co.uk
 * Description: All-in-one SEO content generator with AI-powered bulk content creation, automatic keyword optimization, and smart internal linking. Combines template-based generation, focus keyword assignment, and automated publishing workflow.
 * Version: 1.0.0
 * Author: <PERSON>
 * License: GPL2
 * Text Domain: seo-page-generator-ai
 * Domain Path: /languages
 * 
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SPGA_VERSION', '1.0.0');
define('SPGA_PLUGIN_FILE', __FILE__);
define('SPGA_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SPGA_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SPGA_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main SEO Page Generator AI Class
 */
class SEO_Page_Generator_AI {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Plugin components
     */
    public $admin;
    public $template_engine;
    public $ai_content;
    public $seo_optimizer;
    public $internal_linker;
    public $progress_tracker;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
        $this->init_components();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        register_activation_hook(SPGA_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(SPGA_PLUGIN_FILE, array($this, 'deactivate'));
        
        add_action('init', array($this, 'init'));
        add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
        add_action('wp_enqueue_scripts', array($this, 'frontend_scripts'));
        
        // Cron hooks for background processing
        add_action('spga_process_queue', array($this, 'process_content_queue'));
        add_filter('cron_schedules', array($this, 'add_cron_intervals'));
    }
    
    /**
     * Load required files
     */
    private function load_dependencies() {
        // Core classes
        require_once SPGA_PLUGIN_DIR . 'includes/class-database.php';
        require_once SPGA_PLUGIN_DIR . 'includes/class-template-engine.php';
        require_once SPGA_PLUGIN_DIR . 'includes/class-ai-content.php';
        require_once SPGA_PLUGIN_DIR . 'includes/class-seo-optimizer.php';
        require_once SPGA_PLUGIN_DIR . 'includes/class-internal-linker.php';
        require_once SPGA_PLUGIN_DIR . 'includes/class-progress-tracker.php';
        
        // Admin classes
        if (is_admin()) {
            require_once SPGA_PLUGIN_DIR . 'admin/class-admin.php';
        }
        
        // Migration utilities
        require_once SPGA_PLUGIN_DIR . 'includes/class-migration.php';

        // Default content
        require_once SPGA_PLUGIN_DIR . 'includes/class-default-content.php';
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize core components
        $this->template_engine = new SPGA_Template_Engine();
        $this->ai_content = new SPGA_AI_Content();
        $this->seo_optimizer = new SPGA_SEO_Optimizer();
        $this->internal_linker = new SPGA_Internal_Linker();
        $this->progress_tracker = new SPGA_Progress_Tracker();
        
        // Initialize admin interface
        if (is_admin()) {
            $this->admin = new SPGA_Admin();
            add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        }
    }
    
    /**
     * Plugin initialization
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('seo-page-generator-ai', false, dirname(SPGA_PLUGIN_BASENAME) . '/languages');

        // Initialize database if needed
        $this->maybe_upgrade_database();

        // Schedule cron if not already scheduled
        $this->schedule_cron_jobs();
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'spga') === false && strpos($hook, 'seo-page-generator-ai') === false) {
            return;
        }

        // Enqueue CSS
        wp_enqueue_style(
            'spga-admin-css',
            SPGA_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            SPGA_VERSION
        );

        // Enqueue JavaScript
        wp_enqueue_script(
            'spga-admin-js',
            SPGA_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            SPGA_VERSION,
            true
        );

        // Localize script with AJAX data
        wp_localize_script('spga-admin-js', 'spga_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('spga_nonce'),
            'dashboard_url' => admin_url('admin.php?page=seo-page-generator-ai'),
            'queue_url' => admin_url('admin.php?page=spga-queue'),
            'strings' => array(
                'validation_keywords_locations' => __('Please enter both keywords and locations.', 'seo-page-generator-ai'),
                'validation_csv_file' => __('Please select a CSV file.', 'seo-page-generator-ai'),
                'validation_template' => __('Please select a template.', 'seo-page-generator-ai'),
                'confirm_delete' => __('Are you sure you want to delete this item?', 'seo-page-generator-ai'),
                'confirm_stop_processing' => __('Are you sure you want to stop processing?', 'seo-page-generator-ai')
            )
        ));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        SPGA_Database::create_tables();

        // Schedule cron jobs
        $this->schedule_cron_jobs();

        // Set default options
        $this->set_default_options();

        // Initialize default content if this is a fresh install
        if (!get_option('spga_defaults_initialized')) {
            SPGA_Default_Content::initialize_defaults();
            update_option('spga_defaults_initialized', true);
        }

        // Flush rewrite rules
        flush_rewrite_rules();

        // Log activation
        error_log('SEO Page Generator AI: Plugin activated');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled cron jobs
        wp_clear_scheduled_hook('spga_process_queue');
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Log deactivation
        error_log('SEO Page Generator AI: Plugin deactivated');
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'seo-page-generator-ai') === false) {
            return;
        }
        
        wp_enqueue_style(
            'spga-admin-style',
            SPGA_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            SPGA_VERSION
        );
        
        wp_enqueue_script(
            'spga-admin-script',
            SPGA_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-util'),
            SPGA_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('spga-admin-script', 'spga_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('spga_nonce'),
            'strings' => array(
                'processing' => __('Processing...', 'seo-page-generator-ai'),
                'completed' => __('Completed', 'seo-page-generator-ai'),
                'failed' => __('Failed', 'seo-page-generator-ai'),
                'confirm_delete' => __('Are you sure you want to delete this item?', 'seo-page-generator-ai'),
            )
        ));
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function frontend_scripts() {
        // Frontend styles if needed
        wp_enqueue_style(
            'spga-frontend-style',
            SPGA_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            SPGA_VERSION
        );
    }
    
    /**
     * Add custom cron intervals
     */
    public function add_cron_intervals($schedules) {
        $schedules['spga_every_minute'] = array(
            'interval' => 60,
            'display' => __('Every Minute', 'seo-page-generator-ai')
        );
        
        $schedules['spga_every_five_minutes'] = array(
            'interval' => 300,
            'display' => __('Every 5 Minutes', 'seo-page-generator-ai')
        );
        
        return $schedules;
    }
    
    /**
     * Schedule cron jobs
     */
    private function schedule_cron_jobs() {
        if (!wp_next_scheduled('spga_process_queue')) {
            wp_schedule_event(time(), 'spga_every_five_minutes', 'spga_process_queue');
        }
    }
    
    /**
     * Process content generation queue
     */
    public function process_content_queue() {
        if ($this->progress_tracker) {
            $this->progress_tracker->process_queue();
        }
    }
    
    /**
     * Check if database needs upgrading
     */
    private function maybe_upgrade_database() {
        $current_version = get_option('spga_db_version', '0');
        
        if (version_compare($current_version, SPGA_VERSION, '<')) {
            SPGA_Database::upgrade_tables();
            update_option('spga_db_version', SPGA_VERSION);
        }
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $defaults = array(
            'spga_ai_provider' => 'gemini',
            'spga_gemini_api_key' => '',
            'spga_openai_api_key' => '',
            'spga_default_template' => '',
            'spga_auto_publish' => false,
            'spga_internal_linking' => true,
            'spga_seo_optimization' => true,
            'spga_batch_size' => 5,
            'spga_processing_delay' => 60,
        );
        
        foreach ($defaults as $option => $value) {
            if (get_option($option) === false) {
                add_option($option, $value);
            }
        }
    }
    
    /**
     * Get plugin option with default fallback
     */
    public static function get_option($option, $default = false) {
        return get_option('spga_' . $option, $default);
    }
    
    /**
     * Update plugin option
     */
    public static function update_option($option, $value) {
        return update_option('spga_' . $option, $value);
    }
}

/**
 * Initialize the plugin
 */
function spga_init() {
    return SEO_Page_Generator_AI::get_instance();
}

// Start the plugin
add_action('plugins_loaded', 'spga_init');

/**
 * Helper function to get main plugin instance
 */
function spga() {
    return SEO_Page_Generator_AI::get_instance();
}
