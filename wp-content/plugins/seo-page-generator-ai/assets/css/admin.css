/**
 * Admin CSS for SEO Page Generator AI
 */

/* Workflow Styles */
.spga-workflow-container {
    max-width: 1000px;
    margin: 20px 0;
}

.workflow-progress {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 40px;
    right: 40px;
    height: 2px;
    background: #ddd;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ddd;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #0073aa;
    color: white;
}

.step.completed .step-number {
    background: #46b450;
    color: white;
}

.step-label {
    font-size: 12px;
    color: #666;
    text-align: center;
    font-weight: 500;
}

.step.active .step-label {
    color: #0073aa;
    font-weight: bold;
}

/* Workflow Step Content */
.workflow-step-content {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
}

.step-header {
    margin-bottom: 30px;
    text-align: center;
}

.step-header h2 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.step-header p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

/* Input Method Selector */
.input-method-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.method-option input[type="radio"] {
    display: none;
}

.method-card {
    display: block;
    padding: 20px;
    border: 2px solid #ddd;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.method-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,115,170,0.1);
}

.method-option input[type="radio"]:checked + .method-card {
    border-color: #0073aa;
    background: #f0f8ff;
}

.method-icon {
    font-size: 32px;
    margin-bottom: 15px;
}

.method-card h3 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.method-card p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Input Sections */
.input-section {
    margin-top: 30px;
}

.input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.input-column label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.input-column .description {
    display: block;
    font-weight: normal;
    color: #666;
    font-size: 13px;
    margin-top: 5px;
}

.input-column textarea {
    width: 100%;
    min-height: 200px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
    resize: vertical;
}

/* Combination Preview */
.combination-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.combination-preview h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.combinations-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.combination-item {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #495057;
}

.combinations-count {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}

/* CSV Upload */
.csv-upload-area {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.upload-zone {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-zone:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.upload-zone.dragover {
    border-color: #0073aa;
    background: #e6f3ff;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.upload-zone h3 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.upload-zone p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 14px;
}

.csv-template {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.csv-template h4 {
    margin: 0 0 10px 0;
}

.csv-template p {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #666;
}

/* Template Selector */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.template-option input[type="radio"] {
    display: none;
}

.template-card {
    display: block;
    padding: 20px;
    border: 2px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.template-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,115,170,0.1);
}

.template-option input[type="radio"]:checked + .template-card {
    border-color: #0073aa;
    background: #f0f8ff;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.template-header h3 {
    margin: 0;
    color: #23282d;
}

.default-badge {
    background: #46b450;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.template-description {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.setting-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.setting-group h4 {
    margin: 0 0 15px 0;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.setting-group label {
    display: block;
    margin-bottom: 10px;
    font-size: 14px;
    cursor: pointer;
}

.setting-group input[type="radio"],
.setting-group input[type="checkbox"] {
    margin-right: 8px;
}

.setting-group input[type="number"] {
    width: 80px;
    margin-left: 10px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Review Summary */
.review-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.summary-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.summary-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

/* Generation Progress */
.generation-progress {
    margin-bottom: 30px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #00a0d2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
}

.generation-log {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.generation-log h4 {
    margin: 0 0 15px 0;
}

#generation-log-content {
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
}

/* Navigation */
.workflow-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #ddd;
    margin-top: 30px;
}

/* Utility Classes */
.text-muted {
    color: #6c757d !important;
}

.text-center {
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .input-grid,
    .template-grid,
    .settings-grid,
    .review-summary {
        grid-template-columns: 1fr;
    }
    
    .csv-upload-area {
        grid-template-columns: 1fr;
    }
    
    .progress-steps {
        flex-wrap: wrap;
        gap: 20px;
    }
    
    .progress-steps::before {
        display: none;
    }
}
