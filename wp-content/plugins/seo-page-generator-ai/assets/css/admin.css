/**
 * Admin CSS for SEO Page Generator AI
 */

/* General Styles */
.spga-admin-page {
    background: #f1f1f1;
    margin: 0 -20px -10px -22px;
    padding: 20px;
    min-height: calc(100vh - 32px);
}

.spga-admin-page .wrap {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 30px;
    margin: 0;
    max-width: none;
}

.spga-admin-page h1 {
    color: #23282d;
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 30px 0;
    padding: 0 0 15px 0;
    border-bottom: 2px solid #0073aa;
}

.spga-admin-page h2 {
    color: #23282d;
    font-size: 20px;
    font-weight: 600;
    margin: 30px 0 15px 0;
}

.spga-admin-page h3 {
    color: #555;
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 10px 0;
}

/* Card Styles */
.spga-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.spga-card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e1e1e1;
    padding: 15px 20px;
    font-weight: 600;
    color: #23282d;
}

.spga-card-body {
    padding: 20px;
}

.spga-card-footer {
    background: #f8f9fa;
    border-top: 1px solid #e1e1e1;
    padding: 15px 20px;
    text-align: right;
}

/* Statistics Cards */
.spga-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.spga-stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.spga-stat-card:hover {
    transform: translateY(-2px);
}

.spga-stat-card.green {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.spga-stat-card.orange {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.spga-stat-card.blue {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.spga-stat-card.pending {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.spga-stat-card.processing {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.spga-stat-card.completed {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
    color: #333;
}

.spga-stat-card.failed {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.spga-stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
}

.spga-stat-label {
    font-size: 14px;
    opacity: 0.9;
}

.stat-icon {
    font-size: 24px;
    margin-right: 10px;
}

.stat-content h3 {
    font-size: 28px;
    font-weight: bold;
    margin: 0;
    line-height: 1;
}

.stat-content p {
    font-size: 14px;
    margin: 5px 0 0 0;
    opacity: 0.9;
}

/* Button Styles */
.spga-btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.spga-btn-primary {
    background: #0073aa;
    color: white;
}

.spga-btn-primary:hover {
    background: #005a87;
    color: white;
}

.spga-btn-secondary {
    background: #f1f1f1;
    color: #555;
    border: 1px solid #ddd;
}

.spga-btn-secondary:hover {
    background: #e1e1e1;
    color: #333;
}

.spga-btn-success {
    background: #46b450;
    color: white;
}

.spga-btn-success:hover {
    background: #3a9640;
    color: white;
}

.spga-btn-danger {
    background: #dc3232;
    color: white;
}

.spga-btn-danger:hover {
    background: #c62d2d;
    color: white;
}

/* Form Styles */
.spga-form-group {
    margin-bottom: 20px;
}

.spga-form-label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #23282d;
}

.spga-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.spga-form-control:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

.spga-form-description {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
}

/* Table Styles */
.spga-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.spga-table th,
.spga-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e1e1e1;
}

.spga-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #23282d;
}

.spga-table tr:hover {
    background: #f8f9fa;
}

/* Alert Styles */
.spga-alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid;
}

.spga-alert-success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.spga-alert-warning {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.spga-alert-error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.spga-alert-info {
    background: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Settings Page Specific Styles */
.settings-container {
    max-width: 1200px;
}

.settings-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.spga-card .spga-form-group:last-child {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .spga-stats-grid {
        grid-template-columns: 1fr;
    }

    .spga-stat-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .stat-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .spga-admin-page {
        margin: 0 -10px -10px -12px;
        padding: 10px;
    }

    .spga-admin-page .wrap {
        padding: 20px;
    }
}

/* Loading States */
.spga-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.spga-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error States */
.spga-success {
    border-left: 4px solid #46b450;
    background: #f7fcf0;
    padding: 12px;
    margin: 10px 0;
}

.spga-error {
    border-left: 4px solid #dc3232;
    background: #fef7f7;
    padding: 12px;
    margin: 10px 0;
}

/* Enhanced Button Styles */
.spga-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.spga-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.3);
}

/* Form Validation */
.spga-form-control.error {
    border-color: #dc3232;
    box-shadow: 0 0 0 2px rgba(220,50,50,0.1);
}

.spga-form-control.success {
    border-color: #46b450;
    box-shadow: 0 0 0 2px rgba(70,180,80,0.1);
}

.spga-field-error {
    color: #dc3232;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* Progress Indicators */
.spga-progress {
    width: 100%;
    height: 8px;
    background: #f1f1f1;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.spga-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #0073aa 0%, #005a87 100%);
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* Tooltip Styles */
.spga-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.spga-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s;
    z-index: 1000;
}

.spga-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Combination Preview Styles */
.combinations-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 15px 0;
}

.combination-item {
    background: #f0f6ff;
    border: 1px solid #c3dafe;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    color: #1e40af;
}

.combinations-count {
    font-size: 14px;
    color: #666;
    margin: 10px 0 0 0;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.text-muted {
    color: #999;
    font-style: italic;
}

/* Tab Styles */
.method-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: #0073aa;
    background: #f8f9fa;
}

.tab-button.active {
    color: #0073aa;
    border-bottom-color: #0073aa;
    background: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Input Method Cards */
.input-method-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.method-option {
    position: relative;
}

.method-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.method-card {
    display: block;
    padding: 20px;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    background: #fff;
}

.method-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,115,170,0.1);
}

.method-option input[type="radio"]:checked + .method-card {
    border-color: #0073aa;
    background: #f0f6ff;
    box-shadow: 0 2px 8px rgba(0,115,170,0.2);
}

.method-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.method-card h3 {
    margin: 0 0 8px 0;
    color: #23282d;
}

.method-card p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Bulk Input Grid */
.bulk-input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.bulk-column label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #23282d;
}

.bulk-column .description {
    font-weight: normal;
    color: #666;
    font-size: 13px;
    display: block;
    margin-top: 4px;
}

.bulk-column textarea {
    width: 100%;
    min-height: 200px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
    resize: vertical;
}

.bulk-column textarea:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

/* CSV Upload Styles */
.csv-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    margin: 20px 0;
    transition: border-color 0.3s ease;
}

.csv-upload-area:hover {
    border-color: #0073aa;
}

.upload-zone {
    cursor: pointer;
}

.upload-icon {
    font-size: 48px;
    color: #999;
    margin-bottom: 15px;
}

.upload-zone h3 {
    color: #666;
    margin: 0 0 10px 0;
}

.upload-zone p {
    color: #999;
    margin: 0;
    font-size: 14px;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.processing {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.failed {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.paused {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* Enhanced Input Experience */
.input-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.input-header label {
    flex: 1;
}

.individual-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-item {
    display: flex;
    gap: 10px;
    align-items: center;
}

.input-item .spga-form-control {
    flex: 1;
    margin-bottom: 0;
}

.input-item .remove-input {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    line-height: 1;
    border-radius: 50%;
}

.add-keyword,
.add-location {
    align-self: flex-start;
    margin-top: 5px;
}

.bulk-input {
    margin-top: 10px;
}

.bulk-input textarea {
    width: 100%;
    min-height: 150px;
    font-family: monospace;
    font-size: 14px;
}

/* Toggle Button States */
.toggle-active {
    background: #0073aa !important;
    color: white !important;
}

/* Input Animation */
.input-item {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Combination Preview */
.combination-preview {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e1e1;
}

.combination-preview h4 {
    margin: 0 0 15px 0;
    color: #23282d;
    font-size: 16px;
}

.combinations-preview {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.combination-item {
    display: inline-block;
    margin: 4px;
    padding: 6px 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 13px;
    color: #555;
    transition: all 0.2s ease;
}

.combination-item:hover {
    background: #f0f6ff;
    border-color: #0073aa;
    color: #0073aa;
}

.combinations-count {
    font-size: 14px;
    color: #666;
    margin: 0;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.combinations-count .text-muted {
    color: #999;
    font-style: italic;
}

/* Empty State */
.empty-combinations {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-style: italic;
}

.empty-combinations .icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Responsive Design for Inputs */
@media (max-width: 768px) {
    .input-grid {
        grid-template-columns: 1fr;
    }

    .input-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .input-item {
        flex-direction: column;
        gap: 8px;
    }

    .input-item .spga-form-control {
        width: 100%;
    }

    .input-item .remove-input {
        align-self: flex-end;
        width: auto;
        padding: 6px 12px;
        border-radius: 4px;
    }
}

/* Workflow Styles */
.spga-workflow-container {
    max-width: 1000px;
    margin: 20px 0;
}

.workflow-progress {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 40px;
    right: 40px;
    height: 2px;
    background: #ddd;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ddd;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #0073aa;
    color: white;
}

.step.completed .step-number {
    background: #46b450;
    color: white;
}

.step-label {
    font-size: 12px;
    color: #666;
    text-align: center;
    font-weight: 500;
}

.step.active .step-label {
    color: #0073aa;
    font-weight: bold;
}

/* Workflow Step Content */
.workflow-step-content {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
}

.step-header {
    margin-bottom: 30px;
    text-align: center;
}

.step-header h2 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.step-header p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

/* Input Method Selector */
.input-method-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.method-option input[type="radio"] {
    display: none;
}

.method-card {
    display: block;
    padding: 20px;
    border: 2px solid #ddd;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.method-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,115,170,0.1);
}

.method-option input[type="radio"]:checked + .method-card {
    border-color: #0073aa;
    background: #f0f8ff;
}

.method-icon {
    font-size: 32px;
    margin-bottom: 15px;
}

.method-card h3 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.method-card p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Input Sections */
.input-section {
    margin-top: 30px;
}

.input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.input-column label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.input-column .description {
    display: block;
    font-weight: normal;
    color: #666;
    font-size: 13px;
    margin-top: 5px;
}

.input-column textarea {
    width: 100%;
    min-height: 200px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
    resize: vertical;
}

/* Combination Preview */
.combination-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.combination-preview h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.combinations-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.combination-item {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #495057;
}

.combinations-count {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}

/* CSV Upload */
.csv-upload-area {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.upload-zone {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-zone:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.upload-zone.dragover {
    border-color: #0073aa;
    background: #e6f3ff;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.upload-zone h3 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.upload-zone p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 14px;
}

.csv-template {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.csv-template h4 {
    margin: 0 0 10px 0;
}

.csv-template p {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #666;
}

/* Template Selector */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.template-option input[type="radio"] {
    display: none;
}

.template-card {
    display: block;
    padding: 20px;
    border: 2px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.template-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,115,170,0.1);
}

.template-option input[type="radio"]:checked + .template-card {
    border-color: #0073aa;
    background: #f0f8ff;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.template-header h3 {
    margin: 0;
    color: #23282d;
}

.default-badge {
    background: #46b450;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.template-description {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.setting-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.setting-group h4 {
    margin: 0 0 15px 0;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.setting-group label {
    display: block;
    margin-bottom: 10px;
    font-size: 14px;
    cursor: pointer;
}

.setting-group input[type="radio"],
.setting-group input[type="checkbox"] {
    margin-right: 8px;
}

.setting-group input[type="number"] {
    width: 80px;
    margin-left: 10px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Review Summary */
.review-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.summary-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.summary-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

/* Generation Progress */
.generation-progress {
    margin-bottom: 30px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #00a0d2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
}

.generation-log {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.generation-log h4 {
    margin: 0 0 15px 0;
}

#generation-log-content {
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
}

/* Navigation */
.workflow-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #ddd;
    margin-top: 30px;
}

/* Utility Classes */
.text-muted {
    color: #6c757d !important;
}

.text-center {
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .input-grid,
    .template-grid,
    .settings-grid,
    .review-summary {
        grid-template-columns: 1fr;
    }
    
    .csv-upload-area {
        grid-template-columns: 1fr;
    }
    
    .progress-steps {
        flex-wrap: wrap;
        gap: 20px;
    }
    
    .progress-steps::before {
        display: none;
    }
}
