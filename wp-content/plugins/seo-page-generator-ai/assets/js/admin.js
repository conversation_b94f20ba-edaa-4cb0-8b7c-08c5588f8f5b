/**
 * Admin JavaScript for SEO Page Generator AI
 */

jQuery(document).ready(function($) {

    // Initialize components based on page
    if ($('.spga-workflow').length) {
        initWorkflow();
    }

    if ($('.spga-queue').length) {
        initProgressTracking();
    }

    if ($('.spga-settings').length) {
        initSettings();
    }

    if ($('.spga-keywords').length) {
        initKeywords();
    }

    // Global functionality
    initGlobalFeatures();
    
    /**
     * Initialize workflow functionality
     */
    function initWorkflow() {
        let currentStep = 1;
        const totalSteps = 5;
        
        // Initialize workflow
        updateStepDisplay();
        
        // Step navigation
        $('#next-step').on('click', function() {
            if (validateCurrentStep()) {
                if (currentStep < totalSteps) {
                    currentStep++;
                    updateStepDisplay();
                }
            }
        });
        
        $('#prev-step').on('click', function() {
            if (currentStep > 1) {
                currentStep--;
                updateStepDisplay();
            }
        });
        
        // Submit workflow
        $('#start-generation').on('click', function(e) {
            e.preventDefault();
            
            if (!validateCurrentStep()) {
                return;
            }
            
            // Show step 5 (generation)
            currentStep = 5;
            updateStepDisplay();
            
            // Submit the workflow
            submitWorkflow();
        });
        
        function updateStepDisplay() {
            // Update progress indicator
            $('.progress-steps .step').removeClass('active completed');
            for (let i = 1; i <= totalSteps; i++) {
                if (i < currentStep) {
                    $(`.step[data-step="${i}"]`).addClass('completed');
                } else if (i === currentStep) {
                    $(`.step[data-step="${i}"]`).addClass('active');
                }
            }
            
            // Show/hide step content
            $('.workflow-step-content').hide();
            $(`#step-${currentStep}`).show();
            
            // Update navigation buttons
            $('#prev-step').toggle(currentStep > 1);
            $('#next-step').toggle(currentStep < totalSteps);
            $('#start-generation').toggle(currentStep === totalSteps);
            
            // Update review content if on step 4
            if (currentStep === 4) {
                updateReviewContent();
            }
        }
        
        function validateCurrentStep() {
            switch (currentStep) {
                case 1:
                    return validateInputData();
                case 2:
                    return validateTemplate();
                case 3:
                    return validateSettings();
                case 4:
                    return true; // Review step
                default:
                    return true;
            }
        }
        
        function validateInputData() {
            const method = $('input[name="input_method"]:checked').val();
            
            if (method === 'manual') {
                const keywords = $('#keywords-input').val().trim();
                const locations = $('#locations-input').val().trim();
                
                if (!keywords || !locations) {
                    alert(spga_ajax.strings.validation_keywords_locations || 'Please enter both keywords and locations.');
                    return false;
                }
            } else if (method === 'csv') {
                const file = $('#csv-file-input')[0].files[0];
                if (!file) {
                    alert(spga_ajax.strings.validation_csv_file || 'Please select a CSV file.');
                    return false;
                }
            }
            
            return true;
        }
        
        function validateTemplate() {
            const templateId = $('input[name="template_id"]:checked').val();
            if (!templateId) {
                alert(spga_ajax.strings.validation_template || 'Please select a template.');
                return false;
            }
            return true;
        }
        
        function validateSettings() {
            // Basic validation for settings
            return true;
        }
        
        function updateReviewContent() {
            // Update input data review
            const method = $('input[name="input_method"]:checked').val();
            let inputSummary = '';
            
            if (method === 'manual') {
                const keywords = $('#keywords-input').val().split('\n').filter(k => k.trim());
                const locations = $('#locations-input').val().split('\n').filter(l => l.trim());
                const combinations = keywords.length * locations.length;
                
                inputSummary = `<p><strong>Method:</strong> Manual Entry</p>
                               <p><strong>Keywords:</strong> ${keywords.length}</p>
                               <p><strong>Locations:</strong> ${locations.length}</p>
                               <p><strong>Total Combinations:</strong> ${combinations}</p>`;
            } else {
                inputSummary = `<p><strong>Method:</strong> CSV Upload</p>`;
            }
            
            $('#review-input-data').html(inputSummary);
            
            // Update template review
            const templateName = $('input[name="template_id"]:checked').closest('.template-option').find('h3').text();
            $('#review-template').html(`<p><strong>Template:</strong> ${templateName}</p>`);
            
            // Update settings review
            const postType = $('input[name="post_type"]:checked').val();
            const publishStatus = $('input[name="publish_status"]:checked').val();
            const seoOptimization = $('input[name="enable_seo_optimization"]').is(':checked');
            const internalLinking = $('input[name="enable_internal_linking"]').is(':checked');
            
            const settingsSummary = `<p><strong>Content Type:</strong> ${postType}</p>
                                   <p><strong>Publishing:</strong> ${publishStatus}</p>
                                   <p><strong>SEO Optimization:</strong> ${seoOptimization ? 'Enabled' : 'Disabled'}</p>
                                   <p><strong>Internal Linking:</strong> ${internalLinking ? 'Enabled' : 'Disabled'}</p>`;
            
            $('#review-settings').html(settingsSummary);
            
            // Update output estimate
            if (method === 'manual') {
                const keywords = $('#keywords-input').val().split('\n').filter(k => k.trim());
                const locations = $('#locations-input').val().split('\n').filter(l => l.trim());
                const total = keywords.length * locations.length;
                
                $('#review-output').html(`<p><strong>Estimated Output:</strong> ${total} ${postType}s will be generated</p>`);
            }
        }
        
        function submitWorkflow() {
            const formData = new FormData($('#spga-workflow-form')[0]);
            formData.append('action', 'spga_submit_workflow');
            formData.append('nonce', spga_ajax.nonce);
            
            // Show progress
            $('#generation-progress-fill').css('width', '0%');
            $('#progress-text').text('0% Complete');
            $('#progress-items').text('0 of 0 items processed');
            $('#generation-log-content').html('<p>Starting content generation...</p>');
            
            $.ajax({
                url: spga_ajax.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        $('#generation-log-content').append(`<p>✅ ${response.data.message}</p>`);
                        
                        // Start monitoring progress
                        startProgressMonitoring();
                    } else {
                        $('#generation-log-content').append(`<p>❌ Error: ${response.data}</p>`);
                    }
                },
                error: function() {
                    $('#generation-log-content').append('<p>❌ Failed to submit workflow</p>');
                }
            });
        }
        
        function startProgressMonitoring() {
            const progressInterval = setInterval(function() {
                $.post(spga_ajax.ajax_url, {
                    action: 'spga_get_progress_status',
                    nonce: spga_ajax.nonce
                }, function(response) {
                    if (response.success) {
                        const data = response.data;
                        const stats = data.stats;
                        const total = stats.total;
                        const completed = stats.completed;
                        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
                        
                        // Update progress bar
                        $('#generation-progress-fill').css('width', percentage + '%');
                        $('#progress-text').text(percentage + '% Complete');
                        $('#progress-items').text(`${completed} of ${total} items processed`);
                        
                        // Check if completed
                        if (completed >= total && total > 0) {
                            clearInterval(progressInterval);
                            $('#generation-log-content').append('<p>🎉 Content generation completed!</p>');
                            
                            // Show completion actions
                            $('.generation-actions').show().html(`
                                <a href="${spga_ajax.queue_url || '#'}" class="button button-primary">View Queue</a>
                                <a href="${spga_ajax.dashboard_url || '#'}" class="button">Back to Dashboard</a>
                            `);
                        }
                    }
                });
            }, 5000); // Check every 5 seconds
        }
        
        // Input method toggle
        $('input[name="input_method"]').on('change', function() {
            if ($(this).val() === 'manual') {
                $('#manual-input-section').show();
                $('#csv-input-section').hide();
            } else {
                $('#manual-input-section').hide();
                $('#csv-input-section').show();
            }
        });
        
        // Real-time combination preview
        $('#keywords-input, #locations-input').on('input', function() {
            updateCombinationPreview();
        });
        
        function updateCombinationPreview() {
            const keywords = $('#keywords-input').val().split('\n').filter(k => k.trim());
            const locations = $('#locations-input').val().split('\n').filter(l => l.trim());
            
            if (keywords.length && locations.length) {
                const combinations = [];
                keywords.forEach(keyword => {
                    locations.forEach(location => {
                        combinations.push(`${keyword.trim()} ${location.trim()}`);
                    });
                });
                
                const preview = combinations.slice(0, 10).map(combo => 
                    `<span class="combination-item">${combo}</span>`
                ).join('');
                
                const total = combinations.length;
                const showing = Math.min(10, total);
                
                $('#combinations-list').html(`
                    <div class="combinations-preview">${preview}</div>
                    <p class="combinations-count">
                        Showing ${showing} of ${total} combinations
                        ${total > 10 ? `<span class="text-muted">(${total - 10} more...)</span>` : ''}
                    </p>
                `);
            } else {
                $('#combinations-list').html('<p class="text-muted">Enter keywords and locations above to see the combinations that will be generated.</p>');
            }
        }
    }
    
    /**
     * Initialize progress tracking
     */
    function initProgressTracking() {
        // Auto-refresh progress every 30 seconds
        setInterval(function() {
            updateProgressStatus();
        }, 30000);
        
        // Queue control buttons
        $('#start-processing').on('click', function() {
            $.post(spga_ajax.ajax_url, {
                action: 'spga_start_processing',
                nonce: spga_ajax.nonce
            }, function(response) {
                if (response.success) {
                    $('#processing-status').text('Active');
                    location.reload();
                }
            });
        });
        
        $('#pause-processing').on('click', function() {
            $.post(spga_ajax.ajax_url, {
                action: 'spga_pause_processing',
                nonce: spga_ajax.nonce
            }, function(response) {
                if (response.success) {
                    $('#processing-status').text('Paused');
                }
            });
        });
        
        $('#stop-processing').on('click', function() {
            if (confirm('Are you sure you want to stop processing? This will reset any currently processing items.')) {
                $.post(spga_ajax.ajax_url, {
                    action: 'spga_stop_processing',
                    nonce: spga_ajax.nonce
                }, function(response) {
                    if (response.success) {
                        location.reload();
                    }
                });
            }
        });
        
        $('#retry-failed').on('click', function() {
            $.post(spga_ajax.ajax_url, {
                action: 'spga_retry_failed',
                nonce: spga_ajax.nonce
            }, function(response) {
                if (response.success) {
                    location.reload();
                }
            });
        });
        
        function updateProgressStatus() {
            $.post(spga_ajax.ajax_url, {
                action: 'spga_get_progress_status',
                nonce: spga_ajax.nonce
            }, function(response) {
                if (response.success) {
                    const data = response.data;
                    $('#processing-status').text(data.is_paused ? 'Paused' : 'Active');
                }
            });
        }
    }
    
    // Template preview functionality
    $('.preview-template').on('click', function(e) {
        e.preventDefault();
        const templateId = $(this).data('template-id');
        
        $.post(spga_ajax.ajax_url, {
            action: 'spga_preview_template',
            template_id: templateId,
            nonce: spga_ajax.nonce
        }, function(response) {
            if (response.success) {
                $('#template-preview-content').html(response.data);
                $('#template-preview-modal').show();
            }
        });
    });
    
    // Modal close functionality
    $(document).on('click', '.modal-close, .spga-modal', function(e) {
        if (e.target === this) {
            $('.spga-modal').hide();
        }
    });
    
    // General utility functions
    window.spgaUtils = {
        showNotice: function(message, type = 'success') {
            const notice = $(`<div class="notice notice-${type} is-dismissible"><p>${message}</p></div>`);
            $('.wrap h1').after(notice);
            
            setTimeout(function() {
                notice.fadeOut();
            }, 5000);
        },
        
        confirmAction: function(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }
    };

    /**
     * Initialize settings page functionality
     */
    function initSettings() {
        // Show/hide password fields
        $('.show-password').on('click', function() {
            const target = $(this).data('target');
            const field = $('#' + target);
            const button = $(this);

            if (field.attr('type') === 'password') {
                field.attr('type', 'text');
                button.text('Hide');
            } else {
                field.attr('type', 'password');
                button.text('Show');
            }
        });

        // Form validation
        $('form').on('submit', function(e) {
            let isValid = true;

            // Remove previous error states
            $('.spga-form-control').removeClass('error');
            $('.spga-field-error').remove();

            // Validate required fields
            $(this).find('[required]').each(function() {
                if (!$(this).val().trim()) {
                    $(this).addClass('error');
                    $(this).after('<span class="spga-field-error">This field is required</span>');
                    isValid = false;
                }
            });

            if (!isValid) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: $('.error').first().offset().top - 100
                }, 500);
            }
        });
    }

    /**
     * Initialize keywords page functionality
     */
    function initKeywords() {
        // Tab switching
        $('.tab-button').on('click', function() {
            const tab = $(this).data('tab');

            $('.tab-button').removeClass('active');
            $('.tab-content').removeClass('active');

            $(this).addClass('active');
            $('#' + tab + '-tab').addClass('active');
        });

        // Bulk combination preview
        $('#bulk_keywords, #bulk_locations').on('input', function() {
            updateBulkPreview();
        });

        function updateBulkPreview() {
            const keywords = $('#bulk_keywords').val().split('\n').filter(k => k.trim());
            const locations = $('#bulk_locations').val().split('\n').filter(l => l.trim());

            if (keywords.length && locations.length) {
                const combinations = [];
                keywords.forEach(keyword => {
                    locations.forEach(location => {
                        combinations.push(keyword.trim() + ' in ' + location.trim());
                    });
                });

                const preview = combinations.slice(0, 20).map(combo =>
                    '<span class="combination-item">' + combo + '</span>'
                ).join('');

                const total = combinations.length;
                const showing = Math.min(20, total);

                $('#bulk-combinations-list').html(`
                    <div class="combinations-preview">${preview}</div>
                    <p class="combinations-count">
                        Showing ${showing} of ${total} combinations
                        ${total > 20 ? `<span class="text-muted">(${total - 20} more...)</span>` : ''}
                    </p>
                `);
            } else {
                $('#bulk-combinations-list').html('<p class="text-muted">Enter keywords and locations above to see the combinations that will be created.</p>');
            }
        }
    }

    /**
     * Initialize global features
     */
    function initGlobalFeatures() {
        // Tooltips
        $('.spga-tooltip').on('mouseenter', function() {
            const tooltip = $(this).attr('data-tooltip');
            if (tooltip) {
                $(this).attr('title', tooltip);
            }
        });

        // Confirm dialogs
        $('[data-confirm]').on('click', function(e) {
            const message = $(this).data('confirm');
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });

        // Auto-dismiss notices
        setTimeout(function() {
            $('.notice.is-dismissible').fadeOut();
        }, 5000);

        // Loading states
        $('form').on('submit', function() {
            $(this).find('input[type="submit"]').prop('disabled', true).addClass('spga-loading');
        });

        // AJAX error handling
        $(document).ajaxError(function(event, xhr, settings, error) {
            console.error('AJAX Error:', error);
            if (xhr.status === 403) {
                alert('Session expired. Please refresh the page and try again.');
            }
        });
    }
});
