<?php
/**
 * Quick Diagnostic Script for SEO Page Generator AI
 * Run this file directly to check plugin status
 */

// WordPress bootstrap
require_once('../../../wp-config.php');

if (!defined('ABSPATH')) {
    die('WordPress not found');
}

echo "<h1>SEO Page Generator AI - Quick Diagnostic</h1>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .ok{color:green;} .error{color:red;} .warning{color:orange;}</style>\n";

// 1. Check if plugin is active
echo "<h2>1. Plugin Status</h2>\n";
if (is_plugin_active('seo-page-generator-ai/seo-page-generator-ai.php')) {
    echo "<p class='ok'>✅ Plugin is active</p>\n";
} else {
    echo "<p class='error'>❌ Plugin is not active</p>\n";
    die('Please activate the plugin first.');
}

// 2. Check database tables
echo "<h2>2. Database Tables</h2>\n";
global $wpdb;
$tables = array(
    'spga_queue' => $wpdb->prefix . 'spga_queue',
    'spga_templates' => $wpdb->prefix . 'spga_templates',
    'spga_logs' => $wpdb->prefix . 'spga_logs',
    'spga_keywords' => $wpdb->prefix . 'spga_keywords'
);

$all_tables_exist = true;
foreach ($tables as $name => $full_name) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$full_name'") == $full_name;
    if ($exists) {
        echo "<p class='ok'>✅ $name table exists</p>\n";
    } else {
        echo "<p class='error'>❌ $name table missing</p>\n";
        $all_tables_exist = false;
    }
}

if (!$all_tables_exist) {
    echo "<p><strong>Fix:</strong> Go to SEO Generator AI > Diagnostics and click 'Create Tables'</p>\n";
}

// 3. Check API configuration
echo "<h2>3. API Configuration</h2>\n";
$gemini_key = get_option('spga_gemini_api_key');
$openai_key = get_option('spga_openai_api_key');
$provider = get_option('spga_ai_provider', 'gemini');

echo "<p>Current Provider: <strong>$provider</strong></p>\n";

if ($provider === 'gemini') {
    if (!empty($gemini_key)) {
        echo "<p class='ok'>✅ Gemini API key configured</p>\n";
    } else {
        echo "<p class='error'>❌ Gemini API key missing</p>\n";
        echo "<p><strong>Fix:</strong> Go to SEO Generator AI > Settings and add your Gemini API key</p>\n";
    }
} else {
    if (!empty($openai_key)) {
        echo "<p class='ok'>✅ OpenAI API key configured</p>\n";
    } else {
        echo "<p class='error'>❌ OpenAI API key missing</p>\n";
        echo "<p><strong>Fix:</strong> Go to SEO Generator AI > Settings and add your OpenAI API key</p>\n";
    }
}

// 4. Check WordPress Cron
echo "<h2>4. WordPress Cron</h2>\n";
if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "<p class='error'>❌ WordPress cron is disabled</p>\n";
    echo "<p><strong>Fix:</strong> Remove or comment out 'define('DISABLE_WP_CRON', true);' from wp-config.php</p>\n";
} else {
    echo "<p class='ok'>✅ WordPress cron is enabled</p>\n";
}

$next_cron = wp_next_scheduled('spga_process_queue');
if ($next_cron) {
    echo "<p class='ok'>✅ Queue processing scheduled for: " . date('Y-m-d H:i:s', $next_cron) . "</p>\n";
} else {
    echo "<p class='error'>❌ Queue processing not scheduled</p>\n";
    echo "<p><strong>Fix:</strong> Go to SEO Generator AI > Diagnostics and click 'Schedule Now'</p>\n";
}

// 5. Check queue status
echo "<h2>5. Queue Status</h2>\n";
$is_paused = get_option('spga_queue_paused', false);
if ($is_paused) {
    echo "<p class='warning'>⏸️ Queue is paused</p>\n";
    echo "<p><strong>Fix:</strong> Go to SEO Generator AI > Diagnostics and click 'Unpause'</p>\n";
} else {
    echo "<p class='ok'>✅ Queue is active</p>\n";
}

$processing_lock = get_transient('spga_processing_lock');
if ($processing_lock) {
    echo "<p class='warning'>🔒 Processing lock active (since " . date('Y-m-d H:i:s', $processing_lock) . ")</p>\n";
    echo "<p><strong>Fix:</strong> Go to SEO Generator AI > Diagnostics and click 'Clear Lock'</p>\n";
} else {
    echo "<p class='ok'>✅ No processing lock</p>\n";
}

// 6. Check queue items
echo "<h2>6. Queue Items</h2>\n";
if ($all_tables_exist) {
    $queue_stats = $wpdb->get_row("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
        FROM {$wpdb->prefix}spga_queue
    ");
    
    if ($queue_stats) {
        echo "<p>Total items: <strong>{$queue_stats->total}</strong></p>\n";
        echo "<p>Pending: <strong>{$queue_stats->pending}</strong></p>\n";
        echo "<p>Processing: <strong>{$queue_stats->processing}</strong></p>\n";
        echo "<p>Completed: <strong>{$queue_stats->completed}</strong></p>\n";
        echo "<p>Failed: <strong>{$queue_stats->failed}</strong></p>\n";
        
        if ($queue_stats->total == 0) {
            echo "<p class='warning'>⚠️ No items in queue</p>\n";
            echo "<p><strong>Fix:</strong> Go to SEO Generator AI > Workflow and create some content</p>\n";
        }
    }
}

// 7. Check recent logs
echo "<h2>7. Recent Activity</h2>\n";
if ($all_tables_exist) {
    $recent_logs = $wpdb->get_results("
        SELECT action, status, message, timestamp 
        FROM {$wpdb->prefix}spga_logs 
        ORDER BY timestamp DESC 
        LIMIT 5
    ");
    
    if ($recent_logs) {
        echo "<table border='1' cellpadding='5'>\n";
        echo "<tr><th>Time</th><th>Action</th><th>Status</th><th>Message</th></tr>\n";
        foreach ($recent_logs as $log) {
            $status_class = $log->status === 'success' ? 'ok' : 'error';
            echo "<tr><td>{$log->timestamp}</td><td>{$log->action}</td><td class='$status_class'>{$log->status}</td><td>{$log->message}</td></tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>No recent activity logged</p>\n";
    }
}

// 8. Quick fixes
echo "<h2>8. Quick Actions</h2>\n";
echo "<p><a href='" . admin_url('admin.php?page=spga-diagnostics') . "'>🔧 Go to Diagnostics Page</a></p>\n";
echo "<p><a href='" . admin_url('admin.php?page=spga-settings') . "'>⚙️ Go to Settings Page</a></p>\n";
echo "<p><a href='" . admin_url('admin.php?page=spga-workflow') . "'>📝 Go to Workflow Page</a></p>\n";
echo "<p><a href='" . admin_url('admin.php?page=spga-queue') . "'>📋 Go to Queue Management</a></p>\n";

// 9. Manual processing test
echo "<h2>9. Manual Processing Test</h2>\n";
if (isset($_GET['test_processing']) && $_GET['test_processing'] === '1') {
    echo "<p>Testing manual processing...</p>\n";
    
    // Trigger processing
    do_action('spga_process_queue');
    
    echo "<p class='ok'>✅ Manual processing triggered</p>\n";
    echo "<p>Check the queue management page to see if any items were processed.</p>\n";
} else {
    echo "<p><a href='?test_processing=1'>🚀 Test Manual Processing</a></p>\n";
}

echo "<hr>\n";
echo "<p><strong>Summary:</strong> If all items above show ✅, your plugin should be working correctly.</p>\n";
echo "<p><strong>Still having issues?</strong> Check the <a href='" . plugin_dir_url(__FILE__) . "PROCESSING-SETUP-GUIDE.md'>Processing Setup Guide</a></p>\n";
?>
