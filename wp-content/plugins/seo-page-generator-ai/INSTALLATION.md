# SEO Page Generator AI - Installation & Setup Guide

## 🚀 Quick Installation

### 1. Upload Plugin Files
1. Upload the entire `seo-page-generator-ai` folder to `/wp-content/plugins/`
2. Activate the plugin through the WordPress admin panel
3. The plugin will automatically create database tables and default content

### 2. Initial Setup
1. Go to **SEO Generator AI > Settings**
2. Add your AI API keys:
   - **Google Gemini API Key** (recommended): Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - **OpenAI API Key** (optional): Get from [OpenAI Platform](https://platform.openai.com/api-keys)
3. Test your API connections using the test buttons
4. Configure your preferred settings

### 3. First Content Generation
1. Navigate to **SEO Generator AI > Workflow**
2. Follow the 5-step guided process:
   - **Step 1**: Enter keywords and locations
   - **Step 2**: Select the default template
   - **Step 3**: Configure generation settings
   - **Step 4**: Review your configuration
   - **Step 5**: Start generation and monitor progress

## 📋 System Requirements

- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher
- **Memory**: 256MB recommended for bulk processing
- **API Access**: Google Gemini or OpenAI API key

## 🔧 Configuration Options

### AI Provider Settings
- **Primary Provider**: Choose between Google Gemini (recommended) or OpenAI
- **Fallback**: The other provider will be used if primary fails
- **API Keys**: Both can be configured for maximum reliability

### Content Generation
- **Default Template**: Choose which template to use by default
- **Auto Publish**: Enable to publish content immediately (disabled = drafts)
- **SEO Optimization**: Auto-assign focus keywords and meta data
- **Internal Linking**: Automatically add internal links based on rules

### Processing Options
- **Batch Size**: Number of items to process at once (1-50)
- **Processing Delay**: Delay between batches in seconds (10-3600)

## 📊 Features Overview

### 1. Enhanced Workflow System
- **Step-by-step guidance** for easy content generation
- **Real-time preview** of keyword-location combinations
- **CSV import support** for bulk data entry
- **Progress tracking** with pause/resume functionality

### 2. AI Content Generation
- **Google Gemini integration** (cost-effective, high quality)
- **OpenAI GPT integration** (reliable fallback)
- **Template-based generation** with customizable sections
- **Variable replacement** for dynamic content

### 3. SEO Optimization
- **Automatic focus keyword assignment**
- **Meta title and description generation**
- **Yoast SEO and RankMath integration**
- **Content analysis and recommendations**

### 4. Internal Linking
- **Configurable keyword rules** for targeted linking
- **Automatic link insertion** during content generation
- **Bulk processing** for existing content
- **Link density controls** to prevent over-optimization

### 5. Progress Management
- **Real-time queue monitoring** with detailed statistics
- **Comprehensive logging** for troubleshooting
- **Retry functionality** for failed items
- **Batch processing controls** for performance management

## 🎯 Quick Start Examples

### Example 1: Local Business Pages
**Input:**
- Keywords: "plumber", "electrician", "carpenter"
- Locations: "London", "Manchester", "Birmingham"

**Output:** 9 location-specific service pages with SEO optimization

### Example 2: Debt Advice Pages
**Input:**
- Keywords: "debt help", "debt advice", "IVA"
- Locations: "London", "Liverpool", "Leeds"

**Output:** 9 location-specific debt advice pages with internal linking

## 🔄 Migration from Existing Plugins

The plugin includes migration utilities for:

### Dynamic SEO Content Generator
- **Templates**: Automatically imports existing templates
- **Keywords**: Imports keyword and location lists
- **Settings**: Transfers API keys and configurations
- **Queue Data**: Imports pending items from titles.json

### Auto Focus Keyword for SEO
- **Settings**: Imports post type configurations
- **Keyword assignments**: Preserves existing focus keywords

### Smart Internal Linker
- **Keyword lists**: Imports existing linking keywords
- **API keys**: Transfers OpenAI configuration

**To migrate:**
1. Go to **SEO Generator AI > Dashboard**
2. Look for migration notices
3. Click "Migrate Data" to import from existing plugins
4. Review imported data in respective management pages

## 🛠️ Troubleshooting

### Common Issues

**API Connection Failures**
- Verify API keys are correctly entered
- Check API quotas and billing status
- Test with small batches first

**Generation Failures**
- Check error logs in Queue management
- Verify template shortcodes are properly formatted
- Ensure sufficient server memory for batch processing

**Template Issues**
- Validate shortcode syntax in templates
- Test templates with sample data before bulk generation
- Check for conflicting plugins that modify content

### Debug Mode
Enable WordPress debug mode for detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Performance Optimization
- **Reduce batch size** if experiencing timeouts
- **Increase processing delay** to avoid API rate limits
- **Monitor server resources** during bulk processing
- **Use caching plugins** to improve site performance

## 📈 Best Practices

### Content Quality
1. **Use descriptive keywords** that match user intent
2. **Choose relevant locations** for your target audience
3. **Customize templates** to match your brand voice
4. **Review generated content** before publishing

### SEO Optimization
1. **Enable SEO optimization** for automatic keyword assignment
2. **Use internal linking** to improve site structure
3. **Monitor content quality** and make manual adjustments
4. **Track performance** using analytics tools

### Performance Management
1. **Start with small batches** to test configuration
2. **Monitor API usage** to manage costs
3. **Use appropriate delays** to avoid rate limiting
4. **Schedule processing** during low-traffic periods

## 🔗 Useful Links

- **Google Gemini API**: [Get API Key](https://makersuite.google.com/app/apikey)
- **OpenAI Platform**: [Get API Key](https://platform.openai.com/api-keys)
- **WordPress Codex**: [Plugin Development](https://codex.wordpress.org/Plugin_API)
- **Yoast SEO**: [Documentation](https://yoast.com/help/)
- **RankMath**: [Documentation](https://rankmath.com/kb/)

## 📞 Support

For technical support or questions:
1. Check the **System Status** in Settings
2. Review **error logs** in Queue management
3. Test **API connections** in Settings
4. Consult the **troubleshooting guide** above

---

**Version**: 1.0.0  
**Last Updated**: January 2025  
**Compatibility**: WordPress 5.0+, PHP 7.4+
