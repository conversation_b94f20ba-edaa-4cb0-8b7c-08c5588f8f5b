# SEO Page Generator AI - Development Planning

## Project Overview
Combining three WordPress SEO plugins into one unified solution with enhanced workflow:
1. Dynamic SEO Content Generator (Template + AI content)
2. Auto Focus Keyword for SEO (Automatic keyword assignment)
3. Smart Internal Linker (Automated internal linking)

## Target Workflow
**Goal**: "Put a list of keywords and locations, and let the plugin do everything: generate, optimize, publish."

### Current vs Desired State

#### Current Workflow (Manual & Complicated)
- Manual input of focus keyword, location, and Gemini prompt
- Multiple plugin dependencies
- Manual post creation one at a time
- Limited UI for managing progress

#### Desired Workflow (Automated)
1. **Bulk Input** → CSV upload or manual form (Keywords + Locations + Prompts)
2. **Template Selection** → Choose/customize page template with variables
3. **AI Generation** → Automated content creation using Gemini API
4. **SEO Optimization** → Auto-assign focus keywords, meta titles/descriptions
5. **Internal Linking** → Automatic internal link insertion
6. **Publishing** → Batch publish with progress tracking

## Technical Architecture

### Core Components
1. **Input Management System**
   - CSV upload/download functionality
   - Manual input forms
   - Queue management for bulk processing

2. **Template Engine** (Enhanced from Dynamic SEO)
   - Keep existing shortcode system: `[dsc_dynamic name='section']`
   - Add new variables: `[keyword]`, `[location]`, `[ai_content]`
   - Visual template builder (drag & drop)
   - Multiple template support

3. **AI Content Generation** (From Dynamic SEO)
   - Google Gemini API integration
   - OpenAI fallback support
   - Batch processing with rate limiting
   - Custom prompts per section

4. **SEO Optimization** (From Auto Focus Keyword)
   - Automatic focus keyword assignment
   - Meta title/description generation
   - RankMath/Yoast integration
   - Bulk SEO processing

5. **Internal Linking** (From Smart Internal Linker)
   - Configurable keyword lists
   - Automatic link insertion
   - Link density controls
   - Batch processing

6. **Progress Tracking & Management**
   - Real-time progress dashboard
   - Queue status monitoring
   - Retry/edit functionality
   - Export/import logs

### Database Schema
```sql
-- Main content queue table
CREATE TABLE wp_seo_generator_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    keyword VARCHAR(255),
    location VARCHAR(255),
    custom_prompt TEXT,
    template_id INT,
    status ENUM('pending', 'processing', 'completed', 'failed'),
    post_id INT NULL,
    created_at TIMESTAMP,
    processed_at TIMESTAMP NULL,
    error_message TEXT NULL
);

-- Template management
CREATE TABLE wp_seo_generator_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255),
    content LONGTEXT,
    variables JSON,
    created_at TIMESTAMP
);

-- Progress tracking
CREATE TABLE wp_seo_generator_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    queue_id INT,
    action VARCHAR(100),
    message TEXT,
    timestamp TIMESTAMP
);
```

## Plugin Structure
```
seo-page-generator-ai/
├── seo-page-generator-ai.php (Main plugin file)
├── PLANNING.md (This file)
├── PROGRESS.md (Development progress tracking)
├── README.md (User documentation)
├── assets/
│   ├── css/
│   ├── js/
│   └── templates/
├── includes/
│   ├── class-seo-generator.php
│   ├── class-template-engine.php
│   ├── class-ai-content.php
│   ├── class-seo-optimizer.php
│   ├── class-internal-linker.php
│   └── class-progress-tracker.php
├── admin/
│   ├── class-admin.php
│   ├── views/
│   │   ├── dashboard.php
│   │   ├── workflow.php
│   │   ├── templates.php
│   │   ├── queue.php
│   │   └── settings.php
│   └── assets/
└── migrations/
    └── migration-scripts.php
```

## Development Phases

### Phase 1: Foundation Setup ✓
- [x] Create plugin directory structure
- [x] Set up planning and progress tracking
- [ ] Create main plugin file
- [ ] Establish database schema
- [ ] Basic admin menu structure

### Phase 2: Core Migration
- [ ] Port Dynamic SEO template system
- [ ] Migrate AI content generation
- [ ] Import Auto Focus Keyword functionality
- [ ] Integrate Smart Internal Linker features

### Phase 3: Enhanced Workflow
- [ ] Build step-by-step UI workflow
- [ ] Implement CSV import/export
- [ ] Create progress tracking dashboard
- [ ] Add queue management system

### Phase 4: Testing & Optimization
- [ ] Integration testing
- [ ] Performance optimization
- [ ] User acceptance testing
- [ ] Documentation completion

## Key Features to Preserve
1. **From Dynamic SEO**: Template system, shortcodes, cron automation, Gemini API
2. **From Auto Focus**: Bulk keyword assignment, SEO plugin integration
3. **From Smart Linker**: Keyword-based linking, batch processing

## New Enhanced Features
1. **Unified Dashboard**: Single interface for all operations
2. **CSV Workflow**: Bulk import/export capabilities
3. **Progress Tracking**: Real-time status and retry options
4. **Template Builder**: Visual template creation
5. **Advanced Scheduling**: Flexible publishing options
