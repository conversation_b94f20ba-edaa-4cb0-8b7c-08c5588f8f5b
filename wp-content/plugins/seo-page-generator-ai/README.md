# SEO Page Generator AI

**Version:** 1.0.0  
**Author:** <PERSON>  
**Description:** All-in-one SEO content generator with AI-powered bulk content creation, automatic keyword optimization, and smart internal linking.

## Overview

SEO Page Generator AI combines the functionality of three separate WordPress plugins into one unified, streamlined solution:

1. **Dynamic SEO Content Generator** - Template-based AI content generation
2. **Auto Focus Keyword for SEO** - Automatic keyword assignment and SEO optimization  
3. **Smart Internal Linker** - Automated internal linking with configurable keywords

## Key Features

### 🚀 Enhanced Workflow System
- **Step-by-step onboarding interface** for easy content generation
- **CSV upload support** for bulk keyword and location processing
- **Real-time progress tracking** with pause/resume functionality
- **Template-based content generation** with AI-powered sections

### 🤖 AI Content Generation
- **Google Gemini API integration** (primary)
- **OpenAI API support** (fallback option)
- **Customizable prompts** per content section
- **Variable replacement** system for dynamic content

### 🎯 SEO Optimization
- **Automatic focus keyword assignment** based on content
- **Meta title and description generation**
- **RankMath and Yoast SEO integration**
- **Bulk SEO processing** capabilities

### 🔗 Smart Internal Linking
- **Configurable keyword lists** for targeted linking
- **Automatic link insertion** during content generation
- **Link density controls** to prevent over-optimization
- **Batch processing** for existing content

### 📊 Progress Tracking & Management
- **Real-time queue monitoring** with detailed statistics
- **Comprehensive logging** system for troubleshooting
- **Retry functionality** for failed items
- **Export/import capabilities** for data management

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the WordPress admin
3. Configure your AI API keys in Settings
4. Start creating content with the Workflow interface

## Quick Start Guide

### 1. Configure API Keys
- Go to **SEO Generator AI > Settings**
- Add your Google Gemini API key (recommended)
- Optionally add OpenAI API key as fallback

### 2. Create Your First Workflow
- Navigate to **SEO Generator AI > Workflow**
- Follow the 5-step guided process:
  1. **Input Data** - Add keywords and locations (manual or CSV)
  2. **Template** - Choose or create a content template
  3. **Settings** - Configure generation and publishing options
  4. **Review** - Confirm your settings
  5. **Generate** - Monitor progress and completion

### 3. Monitor Progress
- Use **SEO Generator AI > Queue** to track generation status
- View detailed logs and retry failed items
- Pause/resume processing as needed

## Template System

### Shortcodes
The plugin supports both new and legacy shortcode formats:

#### New Format
```
[spga_dynamic name="section_name" default="fallback content"]
[spga_variable name="keyword" format="title"]
[spga_content section="intro" prompt="Generate introduction" word_count="100"]
```

#### Legacy Support (Dynamic SEO)
```
[dsc_dynamic name="h1"]
[dsc_dynamic name="h1_description"]
```

### Template Variables
Available variables for dynamic replacement:
- `[keyword]` - Primary keyword
- `[location]` - Target location  
- `[title]` - Generated page title
- `[current_date]` - Current date
- `[current_year]` - Current year
- `[site_name]` - Website name
- `[site_url]` - Website URL

## Database Schema

The plugin creates 6 database tables:

1. **`wp_spga_queue`** - Content generation queue
2. **`wp_spga_templates`** - Template management
3. **`wp_spga_logs`** - Activity and error logs
4. **`wp_spga_shortcodes`** - Shortcode definitions
5. **`wp_spga_keywords`** - Keywords and locations
6. **`wp_spga_linking_rules`** - Internal linking rules

## API Requirements

### Google Gemini API
- **Recommended** primary AI provider
- Get API key from Google AI Studio
- More cost-effective for bulk generation

### OpenAI API  
- **Optional** fallback provider
- Requires OpenAI account and API key
- Higher cost but reliable performance

## System Requirements

- **WordPress:** 5.0 or higher
- **PHP:** 7.4 or higher
- **MySQL:** 5.6 or higher
- **Memory:** 256MB recommended for bulk processing

## Migration from Existing Plugins

The plugin includes migration utilities to import data from:
- Dynamic SEO Content Generator (templates, keywords, settings)
- Auto Focus Keyword for SEO (keyword assignments, configurations)
- Smart Internal Linker (linking rules, keyword lists)

## Workflow Examples

### Example 1: Local Business Pages
**Keywords:** "plumber", "electrician", "carpenter"  
**Locations:** "London", "Manchester", "Birmingham"  
**Output:** 9 location-specific service pages

### Example 2: Product + Location Pages  
**Keywords:** "debt help", "debt advice", "IVA"  
**Locations:** "London", "Liverpool", "Leeds"  
**Output:** 9 location-specific debt advice pages

## Troubleshooting

### Common Issues

**API Key Errors**
- Verify API keys are correctly entered in Settings
- Check API quotas and billing status
- Test with a small batch first

**Generation Failures**
- Check error logs in Queue management
- Verify template shortcodes are properly formatted
- Ensure sufficient server memory for batch processing

**Template Issues**
- Validate shortcode syntax in templates
- Test templates with sample data before bulk generation
- Check for conflicting plugins that modify content

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Support & Documentation

- **Plugin Documentation:** Available in WordPress admin
- **Video Tutorials:** Coming soon
- **Support:** Contact plugin author for assistance

## Changelog

### Version 1.0.0
- Initial release combining three plugins
- Enhanced workflow interface
- Comprehensive template system
- Advanced progress tracking
- CSV import/export functionality

## License

GPL2 - Same as WordPress

## Credits

Built by combining and enhancing:
- Dynamic SEO Content Generator by James Brandwood
- Auto Focus Keyword for SEO by Pagup
- Smart Internal Linker by Tracey Redfurn

---

*For the latest updates and documentation, visit the plugin settings page in your WordPress admin.*
