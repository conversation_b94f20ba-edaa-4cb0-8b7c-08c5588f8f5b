<?php
/**
 * Admin interface for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_notices', array($this, 'admin_notices'));
        
        // AJAX handlers
        add_action('wp_ajax_spga_upload_csv', array($this, 'handle_csv_upload'));
        add_action('wp_ajax_spga_process_queue', array($this, 'handle_process_queue'));
        add_action('wp_ajax_spga_get_progress', array($this, 'handle_get_progress'));
        add_action('wp_ajax_spga_pause_queue', array($this, 'handle_pause_queue'));
        add_action('wp_ajax_spga_resume_queue', array($this, 'handle_resume_queue'));
        add_action('wp_ajax_spga_delete_queue_item', array($this, 'handle_delete_queue_item'));
        add_action('wp_ajax_spga_preview_template', array($this, 'handle_preview_template'));
        add_action('wp_ajax_spga_preview_template_content', array($this, 'handle_preview_template_content'));
        add_action('wp_ajax_spga_submit_workflow', array($this, 'handle_submit_workflow'));
        add_action('wp_ajax_spga_manual_process_queue', array($this, 'handle_manual_process_queue'));
    }
    
    /**
     * Add admin menu pages
     */
    public function add_admin_menu() {
        // Main menu page
        add_menu_page(
            __('SEO Page Generator AI', 'seo-page-generator-ai'),
            __('SEO Generator AI', 'seo-page-generator-ai'),
            'manage_options',
            'seo-page-generator-ai',
            array($this, 'dashboard_page'),
            'dashicons-admin-page',
            30
        );
        
        // Workflow submenu
        add_submenu_page(
            'seo-page-generator-ai',
            __('Content Workflow', 'seo-page-generator-ai'),
            __('Workflow', 'seo-page-generator-ai'),
            'manage_options',
            'spga-workflow',
            array($this, 'workflow_page')
        );
        
        // Templates submenu
        add_submenu_page(
            'seo-page-generator-ai',
            __('Templates', 'seo-page-generator-ai'),
            __('Templates', 'seo-page-generator-ai'),
            'manage_options',
            'spga-templates',
            array($this, 'templates_page')
        );
        
        // Queue management submenu
        add_submenu_page(
            'seo-page-generator-ai',
            __('Queue Management', 'seo-page-generator-ai'),
            __('Queue', 'seo-page-generator-ai'),
            'manage_options',
            'spga-queue',
            array($this, 'queue_page')
        );
        
        // Keywords & Locations submenu
        add_submenu_page(
            'seo-page-generator-ai',
            __('Keywords & Locations', 'seo-page-generator-ai'),
            __('Keywords', 'seo-page-generator-ai'),
            'manage_options',
            'spga-keywords',
            array($this, 'keywords_page')
        );
        
        // Internal Linking submenu
        add_submenu_page(
            'seo-page-generator-ai',
            __('Internal Linking', 'seo-page-generator-ai'),
            __('Internal Links', 'seo-page-generator-ai'),
            'manage_options',
            'spga-linking',
            array($this, 'linking_page')
        );
        
        // Settings submenu
        add_submenu_page(
            'seo-page-generator-ai',
            __('Settings', 'seo-page-generator-ai'),
            __('Settings', 'seo-page-generator-ai'),
            'manage_options',
            'spga-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Initialize admin settings
     */
    public function admin_init() {
        // Register settings
        register_setting('spga_settings', 'spga_ai_provider');
        register_setting('spga_settings', 'spga_gemini_api_key');
        register_setting('spga_settings', 'spga_openai_api_key');
        register_setting('spga_settings', 'spga_default_template');
        register_setting('spga_settings', 'spga_auto_publish');
        register_setting('spga_settings', 'spga_internal_linking');
        register_setting('spga_settings', 'spga_seo_optimization');
        register_setting('spga_settings', 'spga_batch_size');
        register_setting('spga_settings', 'spga_processing_delay');
        
        // Add settings sections
        add_settings_section(
            'spga_ai_settings',
            __('AI Configuration', 'seo-page-generator-ai'),
            array($this, 'ai_settings_section_callback'),
            'spga_settings'
        );
        
        add_settings_section(
            'spga_content_settings',
            __('Content Generation', 'seo-page-generator-ai'),
            array($this, 'content_settings_section_callback'),
            'spga_settings'
        );
        
        add_settings_section(
            'spga_processing_settings',
            __('Processing Options', 'seo-page-generator-ai'),
            array($this, 'processing_settings_section_callback'),
            'spga_settings'
        );
        
        // Add settings fields
        $this->add_settings_fields();
    }
    
    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        // AI Provider field
        add_settings_field(
            'spga_ai_provider',
            __('AI Provider', 'seo-page-generator-ai'),
            array($this, 'ai_provider_field_callback'),
            'spga_settings',
            'spga_ai_settings'
        );
        
        // Gemini API Key field
        add_settings_field(
            'spga_gemini_api_key',
            __('Google Gemini API Key', 'seo-page-generator-ai'),
            array($this, 'gemini_api_key_field_callback'),
            'spga_settings',
            'spga_ai_settings'
        );
        
        // OpenAI API Key field
        add_settings_field(
            'spga_openai_api_key',
            __('OpenAI API Key', 'seo-page-generator-ai'),
            array($this, 'openai_api_key_field_callback'),
            'spga_settings',
            'spga_ai_settings'
        );
        
        // Auto Publish field
        add_settings_field(
            'spga_auto_publish',
            __('Auto Publish', 'seo-page-generator-ai'),
            array($this, 'auto_publish_field_callback'),
            'spga_settings',
            'spga_content_settings'
        );
        
        // Internal Linking field
        add_settings_field(
            'spga_internal_linking',
            __('Enable Internal Linking', 'seo-page-generator-ai'),
            array($this, 'internal_linking_field_callback'),
            'spga_settings',
            'spga_content_settings'
        );
        
        // SEO Optimization field
        add_settings_field(
            'spga_seo_optimization',
            __('Enable SEO Optimization', 'seo-page-generator-ai'),
            array($this, 'seo_optimization_field_callback'),
            'spga_settings',
            'spga_content_settings'
        );
        
        // Batch Size field
        add_settings_field(
            'spga_batch_size',
            __('Batch Size', 'seo-page-generator-ai'),
            array($this, 'batch_size_field_callback'),
            'spga_settings',
            'spga_processing_settings'
        );
        
        // Processing Delay field
        add_settings_field(
            'spga_processing_delay',
            __('Processing Delay (seconds)', 'seo-page-generator-ai'),
            array($this, 'processing_delay_field_callback'),
            'spga_settings',
            'spga_processing_settings'
        );
    }
    
    /**
     * Display admin notices
     */
    public function admin_notices() {
        // Check if API keys are configured
        $gemini_key = get_option('spga_gemini_api_key');
        $openai_key = get_option('spga_openai_api_key');
        
        if (empty($gemini_key) && empty($openai_key)) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p>' . sprintf(
                __('SEO Page Generator AI: Please configure your AI API keys in the <a href="%s">settings</a> to start generating content.', 'seo-page-generator-ai'),
                admin_url('admin.php?page=spga-settings')
            ) . '</p>';
            echo '</div>';
        }
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        include SPGA_PLUGIN_DIR . 'admin/views/dashboard.php';
    }
    
    /**
     * Workflow page
     */
    public function workflow_page() {
        include SPGA_PLUGIN_DIR . 'admin/views/workflow.php';
    }
    
    /**
     * Templates page
     */
    public function templates_page() {
        include SPGA_PLUGIN_DIR . 'admin/views/templates.php';
    }
    
    /**
     * Queue management page
     */
    public function queue_page() {
        include SPGA_PLUGIN_DIR . 'admin/views/queue.php';
    }
    
    /**
     * Keywords page
     */
    public function keywords_page() {
        include SPGA_PLUGIN_DIR . 'admin/views/keywords.php';
    }
    
    /**
     * Internal linking page
     */
    public function linking_page() {
        include SPGA_PLUGIN_DIR . 'admin/views/linking.php';
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        include SPGA_PLUGIN_DIR . 'admin/views/settings.php';
    }
    
    /**
     * Settings section callbacks
     */
    public function ai_settings_section_callback() {
        echo '<p>' . __('Configure your AI providers for content generation.', 'seo-page-generator-ai') . '</p>';
    }
    
    public function content_settings_section_callback() {
        echo '<p>' . __('Configure content generation and optimization options.', 'seo-page-generator-ai') . '</p>';
    }
    
    public function processing_settings_section_callback() {
        echo '<p>' . __('Configure batch processing and performance settings.', 'seo-page-generator-ai') . '</p>';
    }
    
    /**
     * Settings field callbacks
     */
    public function ai_provider_field_callback() {
        $value = get_option('spga_ai_provider', 'gemini');
        echo '<select name="spga_ai_provider">';
        echo '<option value="gemini"' . selected($value, 'gemini', false) . '>Google Gemini</option>';
        echo '<option value="openai"' . selected($value, 'openai', false) . '>OpenAI</option>';
        echo '</select>';
        echo '<p class="description">' . __('Choose your preferred AI provider for content generation.', 'seo-page-generator-ai') . '</p>';
    }
    
    public function gemini_api_key_field_callback() {
        $value = get_option('spga_gemini_api_key', '');
        echo '<input type="password" name="spga_gemini_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Enter your Google Gemini API key.', 'seo-page-generator-ai') . '</p>';
    }
    
    public function openai_api_key_field_callback() {
        $value = get_option('spga_openai_api_key', '');
        echo '<input type="password" name="spga_openai_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Enter your OpenAI API key (fallback option).', 'seo-page-generator-ai') . '</p>';
    }
    
    public function auto_publish_field_callback() {
        $value = get_option('spga_auto_publish', false);
        echo '<input type="checkbox" name="spga_auto_publish" value="1"' . checked($value, true, false) . ' />';
        echo '<label>' . __('Automatically publish generated content', 'seo-page-generator-ai') . '</label>';
    }
    
    public function internal_linking_field_callback() {
        $value = get_option('spga_internal_linking', true);
        echo '<input type="checkbox" name="spga_internal_linking" value="1"' . checked($value, true, false) . ' />';
        echo '<label>' . __('Enable automatic internal linking', 'seo-page-generator-ai') . '</label>';
    }
    
    public function seo_optimization_field_callback() {
        $value = get_option('spga_seo_optimization', true);
        echo '<input type="checkbox" name="spga_seo_optimization" value="1"' . checked($value, true, false) . ' />';
        echo '<label>' . __('Enable automatic SEO optimization', 'seo-page-generator-ai') . '</label>';
    }
    
    public function batch_size_field_callback() {
        $value = get_option('spga_batch_size', 5);
        echo '<input type="number" name="spga_batch_size" value="' . esc_attr($value) . '" min="1" max="50" />';
        echo '<p class="description">' . __('Number of items to process in each batch.', 'seo-page-generator-ai') . '</p>';
    }
    
    public function processing_delay_field_callback() {
        $value = get_option('spga_processing_delay', 60);
        echo '<input type="number" name="spga_processing_delay" value="' . esc_attr($value) . '" min="10" max="3600" />';
        echo '<p class="description">' . __('Delay between processing batches (in seconds).', 'seo-page-generator-ai') . '</p>';
    }
    
    /**
     * AJAX Handlers
     */
    public function handle_csv_upload() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Handle CSV upload logic here
        wp_send_json_success('CSV uploaded successfully');
    }
    
    public function handle_process_queue() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Trigger queue processing
        do_action('spga_process_queue');
        wp_send_json_success('Queue processing started');
    }
    
    public function handle_get_progress() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Get progress data
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $stats = $wpdb->get_row("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
            FROM $queue_table
        ");
        
        wp_send_json_success($stats);
    }
    
    public function handle_pause_queue() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Pause queue processing
        update_option('spga_queue_paused', true);
        wp_send_json_success('Queue paused');
    }
    
    public function handle_resume_queue() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Resume queue processing
        update_option('spga_queue_paused', false);
        wp_send_json_success('Queue resumed');
    }
    
    public function handle_delete_queue_item() {
        check_ajax_referer('spga_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $item_id = intval($_POST['item_id']);

        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';

        $result = $wpdb->delete($queue_table, array('id' => $item_id), array('%d'));

        if ($result) {
            wp_send_json_success('Item deleted');
        } else {
            wp_send_json_error('Failed to delete item');
        }
    }

    /**
     * Handle template preview
     */
    public function handle_preview_template() {
        check_ajax_referer('spga_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $template_id = intval($_POST['template_id']);
        $preview_html = spga()->template_engine->get_template_preview($template_id);

        if ($preview_html) {
            wp_send_json_success($preview_html);
        } else {
            wp_send_json_error('Failed to generate preview');
        }
    }

    /**
     * Handle template content preview
     */
    public function handle_preview_template_content() {
        check_ajax_referer('spga_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $content = wp_kses_post($_POST['content']);

        // Create temporary template for preview
        $sample_data = array(
            'keyword' => 'Debt Help',
            'location' => 'London',
            'title' => 'Debt Help London'
        );

        $sample_sections = array(
            'h1' => 'Debt Help London',
            'h1_description' => 'Get professional debt help in London. Our expert advisors are here to help you manage your finances.',
            'h2' => 'How We Can Help You',
            'h2_introduction' => 'Our comprehensive debt management services in London include personalized advice, debt consolidation options, and ongoing support.'
        );

        spga()->template_engine->set_generated_content($sample_sections);
        $preview_html = spga()->template_engine->process_template($content, $sample_data);

        wp_send_json_success($preview_html);
    }

    /**
     * Handle workflow submission
     */
    public function handle_submit_workflow() {
        check_ajax_referer('spga_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Parse form data
        $input_method = sanitize_text_field($_POST['input_method']);
        $template_id = intval($_POST['template_id']);
        $post_type = sanitize_text_field($_POST['post_type']);
        $publish_status = sanitize_text_field($_POST['publish_status']);
        $batch_size = intval($_POST['batch_size']);

        $queue_items = array();

        if ($input_method === 'manual') {
            $keywords = array_filter(array_map('trim', explode("\n", sanitize_textarea_field($_POST['keywords']))));
            $locations = array_filter(array_map('trim', explode("\n", sanitize_textarea_field($_POST['locations']))));

            foreach ($keywords as $keyword) {
                foreach ($locations as $location) {
                    $queue_items[] = array(
                        'keyword' => $keyword,
                        'location' => $location,
                        'custom_prompt' => '',
                        'template_id' => $template_id,
                        'post_type' => $post_type,
                        'priority' => 0
                    );
                }
            }
        } elseif ($input_method === 'csv') {
            // Handle CSV data (would need file upload handling)
            wp_send_json_error('CSV upload not implemented in this demo');
        }

        if (empty($queue_items)) {
            wp_send_json_error('No items to add to queue');
        }

        // Add items to queue
        $added_count = spga()->progress_tracker->add_to_queue($queue_items);

        wp_send_json_success(array(
            'message' => sprintf(__('Added %d items to the queue', 'seo-page-generator-ai'), $added_count),
            'added_count' => $added_count
        ));
    }

    /**
     * Handle manual queue processing (for local development)
     */
    public function handle_manual_process_queue() {
        check_ajax_referer('spga_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Trigger the queue processing manually
        do_action('spga_process_queue');

        wp_send_json_success('Queue processing triggered manually');
    }
}
