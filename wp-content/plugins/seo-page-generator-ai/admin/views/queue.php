<?php
/**
 * Queue Management view for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle bulk actions
if (isset($_POST['action']) && isset($_POST['queue_items'])) {
    check_admin_referer('spga_queue_bulk', 'spga_queue_nonce');
    
    $action = sanitize_text_field($_POST['action']);
    $item_ids = array_map('intval', $_POST['queue_items']);
    
    global $wpdb;
    $queue_table = $wpdb->prefix . 'spga_queue';
    
    switch ($action) {
        case 'delete':
            $placeholders = implode(',', array_fill(0, count($item_ids), '%d'));
            $wpdb->query($wpdb->prepare("DELETE FROM $queue_table WHERE id IN ($placeholders)", $item_ids));
            echo '<div class="notice notice-success"><p>' . sprintf(__('Deleted %d items from queue.', 'seo-page-generator-ai'), count($item_ids)) . '</p></div>';
            break;
            
        case 'retry':
            $placeholders = implode(',', array_fill(0, count($item_ids), '%d'));
            $wpdb->query($wpdb->prepare("UPDATE $queue_table SET status = 'pending', error_message = '' WHERE id IN ($placeholders)", $item_ids));
            echo '<div class="notice notice-success"><p>' . sprintf(__('Reset %d items for retry.', 'seo-page-generator-ai'), count($item_ids)) . '</p></div>';
            break;
            
        case 'pause':
            $placeholders = implode(',', array_fill(0, count($item_ids), '%d'));
            $wpdb->query($wpdb->prepare("UPDATE $queue_table SET status = 'paused' WHERE id IN ($placeholders)", $item_ids));
            echo '<div class="notice notice-success"><p>' . sprintf(__('Paused %d items.', 'seo-page-generator-ai'), count($item_ids)) . '</p></div>';
            break;
    }
}

// Get filter parameters
$status_filter = sanitize_text_field($_GET['status'] ?? 'all');
$search = sanitize_text_field($_GET['s'] ?? '');
$per_page = 20;
$paged = max(1, intval($_GET['paged'] ?? 1));
$offset = ($paged - 1) * $per_page;

// Build query
global $wpdb;
$queue_table = $wpdb->prefix . 'spga_queue';
$templates_table = $wpdb->prefix . 'spga_templates';

$where_conditions = array('1=1');
$query_params = array();

if ($status_filter !== 'all') {
    $where_conditions[] = 'q.status = %s';
    $query_params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = '(q.keyword LIKE %s OR q.location LIKE %s)';
    $query_params[] = '%' . $wpdb->esc_like($search) . '%';
    $query_params[] = '%' . $wpdb->esc_like($search) . '%';
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$total_query = "SELECT COUNT(*) FROM $queue_table q WHERE $where_clause";
$total_items = $wpdb->get_var($wpdb->prepare($total_query, $query_params));

// Get items for current page
$items_query = "
    SELECT q.*, t.name as template_name 
    FROM $queue_table q 
    LEFT JOIN $templates_table t ON q.template_id = t.id 
    WHERE $where_clause 
    ORDER BY q.created_at DESC 
    LIMIT %d OFFSET %d
";

$query_params[] = $per_page;
$query_params[] = $offset;

$queue_items = $wpdb->get_results($wpdb->prepare($items_query, $query_params));

// Calculate pagination
$total_pages = ceil($total_items / $per_page);

// Get statistics
$stats = $wpdb->get_row("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN status = 'paused' THEN 1 ELSE 0 END) as paused
    FROM $queue_table
");
?>

<div class="wrap spga-queue">
    <h1 class="wp-heading-inline"><?php _e('Queue Management', 'seo-page-generator-ai'); ?></h1>
    <a href="<?php echo admin_url('admin.php?page=spga-workflow'); ?>" class="page-title-action">
        <?php _e('Add New Items', 'seo-page-generator-ai'); ?>
    </a>
    
    <hr class="wp-header-end">
    
    <!-- Statistics Cards -->
    <div class="spga-stats-grid">
        <div class="spga-stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->total); ?></h3>
                <p><?php _e('Total Items', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card pending">
            <div class="stat-icon">⏳</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->pending); ?></h3>
                <p><?php _e('Pending', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card processing">
            <div class="stat-icon">⚙️</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->processing); ?></h3>
                <p><?php _e('Processing', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card completed">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->completed); ?></h3>
                <p><?php _e('Completed', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card failed">
            <div class="stat-icon">❌</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->failed); ?></h3>
                <p><?php _e('Failed', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card paused">
            <div class="stat-icon">⏸️</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->paused); ?></h3>
                <p><?php _e('Paused', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
    </div>
    
    <!-- Queue Controls -->
    <div class="spga-queue-controls">
        <div class="queue-actions">
            <button type="button" class="button button-primary" id="start-processing">
                <span class="dashicons dashicons-controls-play"></span>
                <?php _e('Start Processing', 'seo-page-generator-ai'); ?>
            </button>
            
            <button type="button" class="button" id="pause-processing">
                <span class="dashicons dashicons-controls-pause"></span>
                <?php _e('Pause', 'seo-page-generator-ai'); ?>
            </button>
            
            <button type="button" class="button" id="stop-processing">
                <span class="dashicons dashicons-controls-forward"></span>
                <?php _e('Stop', 'seo-page-generator-ai'); ?>
            </button>
            
            <button type="button" class="button" id="retry-failed">
                <span class="dashicons dashicons-update"></span>
                <?php _e('Retry Failed', 'seo-page-generator-ai'); ?>
            </button>
        </div>
        
        <div class="queue-status">
            <span id="processing-status">
                <?php 
                $is_paused = get_option('spga_queue_paused', false);
                echo $is_paused ? __('Paused', 'seo-page-generator-ai') : __('Active', 'seo-page-generator-ai');
                ?>
            </span>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="tablenav top">
        <div class="alignleft actions">
            <form method="get">
                <input type="hidden" name="page" value="spga-queue">
                
                <select name="status">
                    <option value="all"><?php _e('All Statuses', 'seo-page-generator-ai'); ?></option>
                    <option value="pending" <?php selected($status_filter, 'pending'); ?>><?php _e('Pending', 'seo-page-generator-ai'); ?></option>
                    <option value="processing" <?php selected($status_filter, 'processing'); ?>><?php _e('Processing', 'seo-page-generator-ai'); ?></option>
                    <option value="completed" <?php selected($status_filter, 'completed'); ?>><?php _e('Completed', 'seo-page-generator-ai'); ?></option>
                    <option value="failed" <?php selected($status_filter, 'failed'); ?>><?php _e('Failed', 'seo-page-generator-ai'); ?></option>
                    <option value="paused" <?php selected($status_filter, 'paused'); ?>><?php _e('Paused', 'seo-page-generator-ai'); ?></option>
                </select>
                
                <input type="search" name="s" value="<?php echo esc_attr($search); ?>" placeholder="<?php esc_attr_e('Search keywords or locations...', 'seo-page-generator-ai'); ?>">
                
                <input type="submit" class="button" value="<?php esc_attr_e('Filter', 'seo-page-generator-ai'); ?>">
            </form>
        </div>
        
        <div class="tablenav-pages">
            <?php
            if ($total_pages > 1) {
                $page_links = paginate_links(array(
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'prev_text' => __('&laquo;'),
                    'next_text' => __('&raquo;'),
                    'total' => $total_pages,
                    'current' => $paged
                ));
                
                if ($page_links) {
                    echo '<span class="displaying-num">' . sprintf(_n('%s item', '%s items', $total_items), number_format_i18n($total_items)) . '</span>';
                    echo $page_links;
                }
            }
            ?>
        </div>
    </div>
    
    <!-- Queue Table -->
    <form method="post">
        <?php wp_nonce_field('spga_queue_bulk', 'spga_queue_nonce'); ?>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <td class="manage-column column-cb check-column">
                        <input type="checkbox" id="cb-select-all-1">
                    </td>
                    <th><?php _e('Keyword', 'seo-page-generator-ai'); ?></th>
                    <th><?php _e('Location', 'seo-page-generator-ai'); ?></th>
                    <th><?php _e('Template', 'seo-page-generator-ai'); ?></th>
                    <th><?php _e('Status', 'seo-page-generator-ai'); ?></th>
                    <th><?php _e('Post', 'seo-page-generator-ai'); ?></th>
                    <th><?php _e('Created', 'seo-page-generator-ai'); ?></th>
                    <th><?php _e('Actions', 'seo-page-generator-ai'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($queue_items)): ?>
                <tr>
                    <td colspan="8" class="no-items">
                        <?php _e('No queue items found.', 'seo-page-generator-ai'); ?>
                        <a href="<?php echo admin_url('admin.php?page=spga-workflow'); ?>">
                            <?php _e('Add some items to get started!', 'seo-page-generator-ai'); ?>
                        </a>
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach ($queue_items as $item): ?>
                    <tr>
                        <th scope="row" class="check-column">
                            <input type="checkbox" name="queue_items[]" value="<?php echo $item->id; ?>">
                        </th>
                        <td>
                            <strong><?php echo esc_html($item->keyword); ?></strong>
                            <?php if (!empty($item->focus_keyword) && $item->focus_keyword !== $item->keyword): ?>
                                <br><small class="description"><?php echo sprintf(__('Focus: %s', 'seo-page-generator-ai'), esc_html($item->focus_keyword)); ?></small>
                            <?php endif; ?>
                        </td>
                        <td><?php echo esc_html($item->location); ?></td>
                        <td>
                            <?php echo esc_html($item->template_name ?: __('Unknown', 'seo-page-generator-ai')); ?>
                            <br><small class="description"><?php echo esc_html(ucfirst($item->post_type)); ?></small>
                        </td>
                        <td>
                            <span class="status-badge status-<?php echo esc_attr($item->status); ?>">
                                <?php echo esc_html(ucfirst($item->status)); ?>
                            </span>
                            <?php if ($item->status === 'failed' && !empty($item->error_message)): ?>
                                <br><small class="error-message" title="<?php echo esc_attr($item->error_message); ?>">
                                    <?php echo esc_html(wp_trim_words($item->error_message, 10)); ?>
                                </small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($item->post_id): ?>
                                <a href="<?php echo get_edit_post_link($item->post_id); ?>" target="_blank">
                                    <?php echo sprintf(__('Edit Post #%d', 'seo-page-generator-ai'), $item->post_id); ?>
                                </a>
                                <br><a href="<?php echo get_permalink($item->post_id); ?>" target="_blank">
                                    <?php _e('View', 'seo-page-generator-ai'); ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">—</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($item->created_at))); ?>
                            <?php if ($item->processed_at): ?>
                                <br><small class="description">
                                    <?php echo sprintf(__('Processed: %s', 'seo-page-generator-ai'), date_i18n(get_option('time_format'), strtotime($item->processed_at))); ?>
                                </small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="row-actions">
                                <?php if ($item->status === 'failed'): ?>
                                    <span class="retry">
                                        <a href="#" class="retry-item" data-id="<?php echo $item->id; ?>">
                                            <?php _e('Retry', 'seo-page-generator-ai'); ?>
                                        </a> |
                                    </span>
                                <?php endif; ?>
                                
                                <?php if (in_array($item->status, array('pending', 'processing'))): ?>
                                    <span class="pause">
                                        <a href="#" class="pause-item" data-id="<?php echo $item->id; ?>">
                                            <?php _e('Pause', 'seo-page-generator-ai'); ?>
                                        </a> |
                                    </span>
                                <?php endif; ?>
                                
                                <span class="delete">
                                    <a href="#" class="delete-item" data-id="<?php echo $item->id; ?>">
                                        <?php _e('Delete', 'seo-page-generator-ai'); ?>
                                    </a>
                                </span>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        
        <div class="tablenav bottom">
            <div class="alignleft actions bulkactions">
                <select name="action">
                    <option value="-1"><?php _e('Bulk Actions', 'seo-page-generator-ai'); ?></option>
                    <option value="retry"><?php _e('Retry', 'seo-page-generator-ai'); ?></option>
                    <option value="pause"><?php _e('Pause', 'seo-page-generator-ai'); ?></option>
                    <option value="delete"><?php _e('Delete', 'seo-page-generator-ai'); ?></option>
                </select>
                <input type="submit" class="button action" value="<?php esc_attr_e('Apply', 'seo-page-generator-ai'); ?>">
            </div>
        </div>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Auto-refresh progress every 30 seconds
    setInterval(function() {
        updateProgressStatus();
    }, 30000);
    
    // Queue control buttons
    $('#start-processing').on('click', function() {
        $.post(ajaxurl, {
            action: 'spga_start_processing',
            nonce: spga_ajax.nonce
        }, function(response) {
            if (response.success) {
                $('#processing-status').text('<?php _e('Active', 'seo-page-generator-ai'); ?>');
                location.reload();
            }
        });
    });
    
    $('#pause-processing').on('click', function() {
        $.post(ajaxurl, {
            action: 'spga_pause_processing',
            nonce: spga_ajax.nonce
        }, function(response) {
            if (response.success) {
                $('#processing-status').text('<?php _e('Paused', 'seo-page-generator-ai'); ?>');
            }
        });
    });
    
    $('#stop-processing').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to stop processing? This will reset any currently processing items.', 'seo-page-generator-ai'); ?>')) {
            $.post(ajaxurl, {
                action: 'spga_stop_processing',
                nonce: spga_ajax.nonce
            }, function(response) {
                if (response.success) {
                    location.reload();
                }
            });
        }
    });
    
    $('#retry-failed').on('click', function() {
        $.post(ajaxurl, {
            action: 'spga_retry_failed',
            nonce: spga_ajax.nonce
        }, function(response) {
            if (response.success) {
                location.reload();
            }
        });
    });
    
    // Individual item actions
    $('.retry-item').on('click', function(e) {
        e.preventDefault();
        var itemId = $(this).data('id');
        // Implement individual retry
    });
    
    $('.pause-item').on('click', function(e) {
        e.preventDefault();
        var itemId = $(this).data('id');
        // Implement individual pause
    });
    
    $('.delete-item').on('click', function(e) {
        e.preventDefault();
        if (confirm('<?php _e('Are you sure you want to delete this item?', 'seo-page-generator-ai'); ?>')) {
            var itemId = $(this).data('id');
            // Implement individual delete
        }
    });
    
    function updateProgressStatus() {
        $.post(ajaxurl, {
            action: 'spga_get_progress_status',
            nonce: spga_ajax.nonce
        }, function(response) {
            if (response.success) {
                var data = response.data;
                $('#processing-status').text(data.is_paused ? '<?php _e('Paused', 'seo-page-generator-ai'); ?>' : '<?php _e('Active', 'seo-page-generator-ai'); ?>');
                
                // Update stats if they've changed
                // This could be enhanced to update the stat cards dynamically
            }
        });
    }
});
</script>
