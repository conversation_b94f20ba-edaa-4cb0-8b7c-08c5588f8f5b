<?php
/**
 * Diagnostics page for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

// Run diagnostics
$diagnostics = array();

// 1. Check database tables
global $wpdb;
$tables_to_check = array(
    'spga_queue' => $wpdb->prefix . 'spga_queue',
    'spga_templates' => $wpdb->prefix . 'spga_templates',
    'spga_logs' => $wpdb->prefix . 'spga_logs',
    'spga_keywords' => $wpdb->prefix . 'spga_keywords'
);

foreach ($tables_to_check as $table_name => $table_full_name) {
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_full_name'") == $table_full_name;
    $diagnostics['database'][$table_name] = $table_exists;
}

// 2. Check API configuration
$diagnostics['api']['gemini_key'] = !empty(get_option('spga_gemini_api_key'));
$diagnostics['api']['openai_key'] = !empty(get_option('spga_openai_api_key'));
$diagnostics['api']['provider'] = get_option('spga_ai_provider', 'gemini');

// 3. Check cron status
$diagnostics['cron']['wp_cron_disabled'] = defined('DISABLE_WP_CRON') && DISABLE_WP_CRON;
$diagnostics['cron']['next_scheduled'] = wp_next_scheduled('spga_process_queue');
$diagnostics['cron']['current_time'] = time();

// 4. Check queue status
$diagnostics['queue']['paused'] = get_option('spga_queue_paused', false);
$diagnostics['queue']['processing_lock'] = get_transient('spga_processing_lock');

// 5. Check queue items
$pending_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}spga_queue WHERE status = 'pending'");
$processing_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}spga_queue WHERE status = 'processing'");
$completed_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}spga_queue WHERE status = 'completed'");
$failed_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}spga_queue WHERE status = 'failed'");

$diagnostics['queue']['pending'] = (int) $pending_count;
$diagnostics['queue']['processing'] = (int) $processing_count;
$diagnostics['queue']['completed'] = (int) $completed_count;
$diagnostics['queue']['failed'] = (int) $failed_count;

// 6. Check recent logs
$recent_logs = $wpdb->get_results("
    SELECT action, status, message, timestamp 
    FROM {$wpdb->prefix}spga_logs 
    ORDER BY timestamp DESC 
    LIMIT 10
");

$diagnostics['logs'] = $recent_logs;

// Handle manual actions
if (isset($_POST['action']) && check_admin_referer('spga_diagnostics')) {
    switch ($_POST['action']) {
        case 'create_tables':
            SPGA_Database::create_tables();
            echo '<div class="notice notice-success"><p>Database tables created/updated!</p></div>';
            break;
            
        case 'schedule_cron':
            wp_clear_scheduled_hook('spga_process_queue');
            wp_schedule_event(time(), 'spga_every_five_minutes', 'spga_process_queue');
            echo '<div class="notice notice-success"><p>Cron job scheduled!</p></div>';
            break;
            
        case 'manual_process':
            do_action('spga_process_queue');
            echo '<div class="notice notice-success"><p>Manual processing triggered!</p></div>';
            break;
            
        case 'unpause_queue':
            update_option('spga_queue_paused', false);
            echo '<div class="notice notice-success"><p>Queue unpaused!</p></div>';
            break;
            
        case 'clear_lock':
            delete_transient('spga_processing_lock');
            echo '<div class="notice notice-success"><p>Processing lock cleared!</p></div>';
            break;
    }
    
    // Refresh diagnostics after action
    header('Location: ' . admin_url('admin.php?page=spga-diagnostics'));
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('SEO Generator AI - Diagnostics', 'seo-page-generator-ai'); ?></h1>
    
    <div class="spga-diagnostics">
        
        <!-- Database Status -->
        <div class="diagnostic-section">
            <h2>📊 Database Status</h2>
            <table class="widefat">
                <thead>
                    <tr>
                        <th>Table</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($diagnostics['database'] as $table => $exists): ?>
                    <tr>
                        <td><?php echo esc_html($table); ?></td>
                        <td>
                            <?php if ($exists): ?>
                                <span style="color: green;">✅ Exists</span>
                            <?php else: ?>
                                <span style="color: red;">❌ Missing</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if (!$exists): ?>
                                <form method="post" style="display: inline;">
                                    <?php wp_nonce_field('spga_diagnostics'); ?>
                                    <input type="hidden" name="action" value="create_tables">
                                    <button type="submit" class="button button-primary">Create Tables</button>
                                </form>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- API Configuration -->
        <div class="diagnostic-section">
            <h2>🔑 API Configuration</h2>
            <table class="widefat">
                <tbody>
                    <tr>
                        <td>Current Provider</td>
                        <td><?php echo esc_html($diagnostics['api']['provider']); ?></td>
                    </tr>
                    <tr>
                        <td>Gemini API Key</td>
                        <td><?php echo $diagnostics['api']['gemini_key'] ? '<span style="color: green;">✅ Configured</span>' : '<span style="color: red;">❌ Missing</span>'; ?></td>
                    </tr>
                    <tr>
                        <td>OpenAI API Key</td>
                        <td><?php echo $diagnostics['api']['openai_key'] ? '<span style="color: green;">✅ Configured</span>' : '<span style="color: red;">❌ Missing</span>'; ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Cron Status -->
        <div class="diagnostic-section">
            <h2>⏰ Cron Status</h2>
            <table class="widefat">
                <tbody>
                    <tr>
                        <td>WP Cron Status</td>
                        <td>
                            <?php if ($diagnostics['cron']['wp_cron_disabled']): ?>
                                <span style="color: red;">❌ Disabled</span>
                            <?php else: ?>
                                <span style="color: green;">✅ Enabled</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td>Next Scheduled</td>
                        <td>
                            <?php if ($diagnostics['cron']['next_scheduled']): ?>
                                <span style="color: green;">✅ <?php echo date('Y-m-d H:i:s', $diagnostics['cron']['next_scheduled']); ?></span>
                            <?php else: ?>
                                <span style="color: red;">❌ Not scheduled</span>
                                <form method="post" style="display: inline; margin-left: 10px;">
                                    <?php wp_nonce_field('spga_diagnostics'); ?>
                                    <input type="hidden" name="action" value="schedule_cron">
                                    <button type="submit" class="button button-secondary">Schedule Now</button>
                                </form>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td>Current Time</td>
                        <td><?php echo date('Y-m-d H:i:s', $diagnostics['cron']['current_time']); ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Queue Status -->
        <div class="diagnostic-section">
            <h2>📋 Queue Status</h2>
            <table class="widefat">
                <tbody>
                    <tr>
                        <td>Queue Status</td>
                        <td>
                            <?php if ($diagnostics['queue']['paused']): ?>
                                <span style="color: orange;">⏸️ Paused</span>
                                <form method="post" style="display: inline; margin-left: 10px;">
                                    <?php wp_nonce_field('spga_diagnostics'); ?>
                                    <input type="hidden" name="action" value="unpause_queue">
                                    <button type="submit" class="button button-secondary">Unpause</button>
                                </form>
                            <?php else: ?>
                                <span style="color: green;">▶️ Active</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td>Processing Lock</td>
                        <td>
                            <?php if ($diagnostics['queue']['processing_lock']): ?>
                                <span style="color: orange;">🔒 Locked</span>
                                <form method="post" style="display: inline; margin-left: 10px;">
                                    <?php wp_nonce_field('spga_diagnostics'); ?>
                                    <input type="hidden" name="action" value="clear_lock">
                                    <button type="submit" class="button button-secondary">Clear Lock</button>
                                </form>
                            <?php else: ?>
                                <span style="color: green;">🔓 Unlocked</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td>Pending Items</td>
                        <td><?php echo $diagnostics['queue']['pending']; ?></td>
                    </tr>
                    <tr>
                        <td>Processing Items</td>
                        <td><?php echo $diagnostics['queue']['processing']; ?></td>
                    </tr>
                    <tr>
                        <td>Completed Items</td>
                        <td><?php echo $diagnostics['queue']['completed']; ?></td>
                    </tr>
                    <tr>
                        <td>Failed Items</td>
                        <td><?php echo $diagnostics['queue']['failed']; ?></td>
                    </tr>
                </tbody>
            </table>
            
            <div style="margin-top: 15px;">
                <form method="post" style="display: inline;">
                    <?php wp_nonce_field('spga_diagnostics'); ?>
                    <input type="hidden" name="action" value="manual_process">
                    <button type="submit" class="button button-primary">🚀 Manual Process Queue</button>
                </form>
            </div>
        </div>
        
        <!-- Recent Logs -->
        <div class="diagnostic-section">
            <h2>📝 Recent Logs</h2>
            <?php if (!empty($diagnostics['logs'])): ?>
                <table class="widefat">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Action</th>
                            <th>Status</th>
                            <th>Message</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($diagnostics['logs'] as $log): ?>
                        <tr>
                            <td><?php echo esc_html($log->timestamp); ?></td>
                            <td><?php echo esc_html($log->action); ?></td>
                            <td>
                                <?php if ($log->status === 'success'): ?>
                                    <span style="color: green;">✅ Success</span>
                                <?php else: ?>
                                    <span style="color: red;">❌ Error</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html($log->message); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>No recent logs found.</p>
            <?php endif; ?>
        </div>
        
    </div>
</div>

<style>
.spga-diagnostics .diagnostic-section {
    margin-bottom: 30px;
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.spga-diagnostics .diagnostic-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

.spga-diagnostics .widefat {
    margin-top: 10px;
}

.spga-diagnostics .widefat td {
    padding: 10px;
}
</style>
