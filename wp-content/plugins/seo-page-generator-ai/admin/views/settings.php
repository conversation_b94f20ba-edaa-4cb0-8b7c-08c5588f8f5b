<?php
/**
 * Settings view for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && check_admin_referer('spga_settings_nonce', 'spga_settings_nonce')) {
    // Update settings
    $settings_updated = array();

    $settings_to_update = array(
        'spga_ai_provider' => 'sanitize_text_field',
        'spga_gemini_api_key' => 'sanitize_text_field',
        'spga_openai_api_key' => 'sanitize_text_field',
        'spga_default_template' => 'intval',
        'spga_auto_publish' => 'intval',
        'spga_internal_linking' => 'intval',
        'spga_seo_optimization' => 'intval',
        'spga_batch_size' => 'intval',
        'spga_processing_delay' => 'intval'
    );

    foreach ($settings_to_update as $setting => $sanitize_function) {
        if (isset($_POST[$setting])) {
            $value = call_user_func($sanitize_function, $_POST[$setting]);
            update_option($setting, $value);
            $settings_updated[] = $setting;
        }
    }

    if (!empty($settings_updated)) {
        echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'seo-page-generator-ai') . '</p></div>';
    }
}

// Test API connection
if (isset($_POST['test_api'])) {
    check_admin_referer('spga_test_api');
    
    $provider = sanitize_text_field($_POST['test_provider']);
    $test_result = spga()->ai_content->test_api_connection($provider);
    
    if ($test_result) {
        echo '<div class="notice notice-success"><p>' . sprintf(__('%s API connection successful!', 'seo-page-generator-ai'), ucfirst($provider)) . '</p></div>';
    } else {
        echo '<div class="notice notice-error"><p>' . sprintf(__('%s API connection failed. Please check your API key.', 'seo-page-generator-ai'), ucfirst($provider)) . '</p></div>';
    }
}

// Get current settings
$current_settings = array();
foreach (array('spga_ai_provider', 'spga_gemini_api_key', 'spga_openai_api_key', 'spga_default_template', 'spga_auto_publish', 'spga_internal_linking', 'spga_seo_optimization', 'spga_batch_size', 'spga_processing_delay') as $setting) {
    $current_settings[$setting] = get_option($setting, '');
}

// Get templates for dropdown
$templates = spga()->template_engine->get_templates();

// Get API usage statistics
$api_usage = spga()->ai_content->get_api_usage();

// Get SEO plugin info
$seo_plugin = spga()->seo_optimizer->get_active_seo_plugin();
$seo_capabilities = spga()->seo_optimizer->get_seo_capabilities();
?>

<div class="spga-admin-page">
    <div class="wrap spga-settings">
        <h1><?php _e('SEO Page Generator AI Settings', 'seo-page-generator-ai'); ?></h1>

        <div class="settings-container">
        <!-- Settings Form -->
        <form method="post" action="">
            <?php wp_nonce_field('spga_settings_nonce', 'spga_settings_nonce'); ?>
            
            <div class="settings-sections">
                <!-- AI Configuration Section -->
                <div class="spga-card">
                    <div class="spga-card-header">
                        <h2><?php _e('AI Configuration', 'seo-page-generator-ai'); ?></h2>
                    </div>
                    <div class="spga-card-body">
                        <p class="spga-form-description"><?php _e('Configure your AI providers for content generation.', 'seo-page-generator-ai'); ?></p>
                    
                        <div class="spga-form-group">
                            <label for="spga_ai_provider" class="spga-form-label"><?php _e('Primary AI Provider', 'seo-page-generator-ai'); ?></label>
                            <select name="spga_ai_provider" id="spga_ai_provider" class="spga-form-control">
                                <option value="gemini" <?php selected($current_settings['spga_ai_provider'], 'gemini'); ?>>
                                    <?php _e('Google Gemini (Recommended)', 'seo-page-generator-ai'); ?>
                                </option>
                                <option value="openai" <?php selected($current_settings['spga_ai_provider'], 'openai'); ?>>
                                    <?php _e('OpenAI GPT', 'seo-page-generator-ai'); ?>
                                </option>
                            </select>
                            <p class="spga-form-description"><?php _e('Choose your preferred AI provider. The other will be used as fallback.', 'seo-page-generator-ai'); ?></p>
                        </div>

                        <div class="spga-form-group">
                            <label for="spga_gemini_api_key" class="spga-form-label"><?php _e('Google Gemini API Key', 'seo-page-generator-ai'); ?></label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="password" name="spga_gemini_api_key" id="spga_gemini_api_key" value="<?php echo esc_attr($current_settings['spga_gemini_api_key']); ?>" class="spga-form-control" style="flex: 1;">
                                <button type="button" class="spga-btn spga-btn-secondary show-password" data-target="spga_gemini_api_key">
                                    <?php _e('Show', 'seo-page-generator-ai'); ?>
                                </button>
                            </div>
                            <p class="spga-form-description">
                                <?php printf(__('Get your API key from <a href="%s" target="_blank">Google AI Studio</a>.', 'seo-page-generator-ai'), 'https://makersuite.google.com/app/apikey'); ?>
                            </p>
                        </div>
                        
                        <tr>
                            <th scope="row">
                                <label for="spga_openai_api_key"><?php _e('OpenAI API Key', 'seo-page-generator-ai'); ?></label>
                            </th>
                            <td>
                                <input type="password" name="spga_openai_api_key" id="spga_openai_api_key" value="<?php echo esc_attr($current_settings['spga_openai_api_key']); ?>" class="regular-text">
                                <button type="button" class="button show-password" data-target="spga_openai_api_key">
                                    <?php _e('Show', 'seo-page-generator-ai'); ?>
                                </button>
                                <p class="description">
                                    <?php printf(__('Get your API key from <a href="%s" target="_blank">OpenAI Platform</a>.', 'seo-page-generator-ai'), 'https://platform.openai.com/api-keys'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                    
                    <!-- API Test Section -->
                    <div class="api-test-section">
                        <h3><?php _e('Test API Connection', 'seo-page-generator-ai'); ?></h3>
                        <form method="post" style="display: inline;">
                            <?php wp_nonce_field('spga_test_api'); ?>
                            <input type="hidden" name="test_provider" value="gemini">
                            <input type="submit" name="test_api" class="button button-secondary" value="<?php esc_attr_e('Test Gemini API', 'seo-page-generator-ai'); ?>">
                        </form>
                        
                        <form method="post" style="display: inline;">
                            <?php wp_nonce_field('spga_test_api'); ?>
                            <input type="hidden" name="test_provider" value="openai">
                            <input type="submit" name="test_api" class="button button-secondary" value="<?php esc_attr_e('Test OpenAI API', 'seo-page-generator-ai'); ?>">
                        </form>
                    </div>
                </div>
                
                <!-- Content Generation Section -->
                <div class="settings-section">
                    <h2><?php _e('Content Generation', 'seo-page-generator-ai'); ?></h2>
                    <p class="description"><?php _e('Configure content generation and optimization options.', 'seo-page-generator-ai'); ?></p>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="spga_default_template"><?php _e('Default Template', 'seo-page-generator-ai'); ?></label>
                            </th>
                            <td>
                                <select name="spga_default_template" id="spga_default_template">
                                    <option value=""><?php _e('No Default Template', 'seo-page-generator-ai'); ?></option>
                                    <?php foreach ($templates as $template): ?>
                                        <option value="<?php echo $template->id; ?>" <?php selected($current_settings['spga_default_template'], $template->id); ?>>
                                            <?php echo esc_html($template->name); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <p class="description"><?php _e('Default template to use in the workflow if none is selected.', 'seo-page-generator-ai'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Auto Publish', 'seo-page-generator-ai'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="spga_auto_publish" value="1" <?php checked($current_settings['spga_auto_publish'], 1); ?>>
                                    <?php _e('Automatically publish generated content', 'seo-page-generator-ai'); ?>
                                </label>
                                <p class="description"><?php _e('If disabled, content will be saved as drafts for manual review.', 'seo-page-generator-ai'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('SEO Optimization', 'seo-page-generator-ai'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="spga_seo_optimization" value="1" <?php checked($current_settings['spga_seo_optimization'], 1); ?>>
                                    <?php _e('Enable automatic SEO optimization', 'seo-page-generator-ai'); ?>
                                </label>
                                <p class="description"><?php _e('Automatically assign focus keywords, meta titles, and descriptions.', 'seo-page-generator-ai'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Internal Linking', 'seo-page-generator-ai'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="spga_internal_linking" value="1" <?php checked($current_settings['spga_internal_linking'], 1); ?>>
                                    <?php _e('Enable automatic internal linking', 'seo-page-generator-ai'); ?>
                                </label>
                                <p class="description"><?php _e('Automatically add internal links based on configured keywords.', 'seo-page-generator-ai'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <!-- Processing Options Section -->
                <div class="settings-section">
                    <h2><?php _e('Processing Options', 'seo-page-generator-ai'); ?></h2>
                    <p class="description"><?php _e('Configure batch processing and performance settings.', 'seo-page-generator-ai'); ?></p>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="spga_batch_size"><?php _e('Batch Size', 'seo-page-generator-ai'); ?></label>
                            </th>
                            <td>
                                <input type="number" name="spga_batch_size" id="spga_batch_size" value="<?php echo esc_attr($current_settings['spga_batch_size'] ?: 5); ?>" min="1" max="50" class="small-text">
                                <p class="description"><?php _e('Number of items to process in each batch. Lower values reduce server load.', 'seo-page-generator-ai'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="spga_processing_delay"><?php _e('Processing Delay', 'seo-page-generator-ai'); ?></label>
                            </th>
                            <td>
                                <input type="number" name="spga_processing_delay" id="spga_processing_delay" value="<?php echo esc_attr($current_settings['spga_processing_delay'] ?: 60); ?>" min="10" max="3600" class="small-text">
                                <span><?php _e('seconds', 'seo-page-generator-ai'); ?></span>
                                <p class="description"><?php _e('Delay between processing batches to avoid API rate limits.', 'seo-page-generator-ai'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <p class="submit">
                <input type="submit" name="submit" class="button button-primary" value="<?php esc_attr_e('Save Settings', 'seo-page-generator-ai'); ?>">
            </p>
        </form>
        
        <!-- System Information -->
        <div class="system-info">
            <h2><?php _e('System Information', 'seo-page-generator-ai'); ?></h2>
            
            <div class="info-grid">
                <!-- API Usage Stats -->
                <div class="info-card">
                    <h3><?php _e('API Usage (Last 30 Days)', 'seo-page-generator-ai'); ?></h3>
                    <div class="stats-list">
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Total Requests:', 'seo-page-generator-ai'); ?></span>
                            <span class="stat-value"><?php echo number_format($api_usage->total_requests ?? 0); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Successful:', 'seo-page-generator-ai'); ?></span>
                            <span class="stat-value"><?php echo number_format($api_usage->successful_requests ?? 0); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Failed:', 'seo-page-generator-ai'); ?></span>
                            <span class="stat-value"><?php echo number_format($api_usage->failed_requests ?? 0); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Content Generated:', 'seo-page-generator-ai'); ?></span>
                            <span class="stat-value"><?php echo number_format($api_usage->content_generated ?? 0); ?></span>
                        </div>
                    </div>
                </div>
                
                <!-- SEO Plugin Integration -->
                <div class="info-card">
                    <h3><?php _e('SEO Plugin Integration', 'seo-page-generator-ai'); ?></h3>
                    <div class="integration-status">
                        <?php if ($seo_plugin): ?>
                            <div class="status-item status-ok">
                                <span class="status-icon">✅</span>
                                <span><?php echo sprintf(__('%s detected and active', 'seo-page-generator-ai'), ucfirst($seo_plugin)); ?></span>
                            </div>
                            
                            <div class="capabilities-list">
                                <h4><?php _e('Available Features:', 'seo-page-generator-ai'); ?></h4>
                                <ul>
                                    <?php foreach ($seo_capabilities as $capability => $available): ?>
                                        <li class="<?php echo $available ? 'available' : 'unavailable'; ?>">
                                            <?php echo $available ? '✅' : '❌'; ?>
                                            <?php echo esc_html(ucwords(str_replace('_', ' ', $capability))); ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php else: ?>
                            <div class="status-item status-warning">
                                <span class="status-icon">⚠️</span>
                                <span><?php _e('No SEO plugin detected', 'seo-page-generator-ai'); ?></span>
                            </div>
                            <p class="description">
                                <?php _e('Install Yoast SEO or RankMath for enhanced SEO features.', 'seo-page-generator-ai'); ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- System Status -->
                <div class="info-card">
                    <h3><?php _e('System Status', 'seo-page-generator-ai'); ?></h3>
                    <div class="status-list">
                        <div class="status-item">
                            <span class="status-label"><?php _e('WordPress Version:', 'seo-page-generator-ai'); ?></span>
                            <span class="status-value"><?php echo get_bloginfo('version'); ?></span>
                        </div>
                        <div class="status-item">
                            <span class="status-label"><?php _e('PHP Version:', 'seo-page-generator-ai'); ?></span>
                            <span class="status-value"><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="status-item">
                            <span class="status-label"><?php _e('Memory Limit:', 'seo-page-generator-ai'); ?></span>
                            <span class="status-value"><?php echo ini_get('memory_limit'); ?></span>
                        </div>
                        <div class="status-item">
                            <span class="status-label"><?php _e('Max Execution Time:', 'seo-page-generator-ai'); ?></span>
                            <span class="status-value"><?php echo ini_get('max_execution_time'); ?>s</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label"><?php _e('Cron Status:', 'seo-page-generator-ai'); ?></span>
                            <span class="status-value">
                                <?php 
                                $next_cron = wp_next_scheduled('spga_process_queue');
                                echo $next_cron ? __('Active', 'seo-page-generator-ai') : __('Inactive', 'seo-page-generator-ai');
                                ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.settings-container {
    max-width: 1200px;
}

.settings-sections {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 30px;
}

.settings-section {
    padding: 30px;
    border-bottom: 1px solid #eee;
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h2 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.api-test-section {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.api-test-section h3 {
    margin: 0 0 15px 0;
}

.show-password {
    margin-left: 10px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.info-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
}

.info-card h3 {
    margin: 0 0 15px 0;
    color: #23282d;
}

.stats-list, .status-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.stat-item, .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.stat-item:last-child, .status-item:last-child {
    border-bottom: none;
}

.stat-label, .status-label {
    font-weight: 500;
    color: #555;
}

.stat-value, .status-value {
    font-weight: bold;
    color: #23282d;
}

.integration-status .status-item {
    justify-content: flex-start;
    gap: 10px;
}

.status-ok {
    color: #155724;
}

.status-warning {
    color: #856404;
}

.capabilities-list {
    margin-top: 15px;
}

.capabilities-list h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.capabilities-list ul {
    margin: 0;
    padding-left: 20px;
}

.capabilities-list li {
    margin-bottom: 5px;
    font-size: 14px;
}

.capabilities-list li.available {
    color: #155724;
}

.capabilities-list li.unavailable {
    color: #721c24;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Show/hide password fields
    $('.show-password').on('click', function() {
        var target = $(this).data('target');
        var field = $('#' + target);
        var button = $(this);
        
        if (field.attr('type') === 'password') {
            field.attr('type', 'text');
            button.text('<?php _e('Hide', 'seo-page-generator-ai'); ?>');
        } else {
            field.attr('type', 'password');
            button.text('<?php _e('Show', 'seo-page-generator-ai'); ?>');
        }
    });
});
</script>
    </div>
</div>
