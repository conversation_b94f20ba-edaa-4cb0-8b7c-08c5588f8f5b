<?php
/**
 * Dashboard view for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get queue statistics
global $wpdb;
$queue_table = $wpdb->prefix . 'spga_queue';
$templates_table = $wpdb->prefix . 'spga_templates';

$queue_stats = $wpdb->get_row("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN status = 'paused' THEN 1 ELSE 0 END) as paused
    FROM $queue_table
");

$template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_active = 1");

// Check if API keys are configured
$gemini_key = get_option('spga_gemini_api_key');
$openai_key = get_option('spga_openai_api_key');
$api_configured = !empty($gemini_key) || !empty($openai_key);

// Get recent activity
$recent_items = $wpdb->get_results("
    SELECT keyword, location, status, created_at, processed_at 
    FROM $queue_table 
    ORDER BY created_at DESC 
    LIMIT 10
");
?>

<div class="wrap spga-dashboard">
    <h1 class="wp-heading-inline">
        <?php _e('SEO Page Generator AI', 'seo-page-generator-ai'); ?>
        <span class="title-count"><?php echo sprintf(__('v%s', 'seo-page-generator-ai'), SPGA_VERSION); ?></span>
    </h1>
    
    <hr class="wp-header-end">
    
    <?php if (!$api_configured): ?>
    <div class="notice notice-warning">
        <p>
            <strong><?php _e('Setup Required:', 'seo-page-generator-ai'); ?></strong>
            <?php printf(
                __('Please configure your AI API keys in <a href="%s">Settings</a> to start generating content.', 'seo-page-generator-ai'),
                admin_url('admin.php?page=spga-settings')
            ); ?>
        </p>
    </div>
    <?php endif; ?>
    
    <!-- Quick Stats Cards -->
    <div class="spga-stats-grid">
        <div class="spga-stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
                <h3><?php echo number_format($queue_stats->total ?? 0); ?></h3>
                <p><?php _e('Total Items', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card pending">
            <div class="stat-icon">⏳</div>
            <div class="stat-content">
                <h3><?php echo number_format($queue_stats->pending ?? 0); ?></h3>
                <p><?php _e('Pending', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card processing">
            <div class="stat-icon">⚙️</div>
            <div class="stat-content">
                <h3><?php echo number_format($queue_stats->processing ?? 0); ?></h3>
                <p><?php _e('Processing', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card completed">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
                <h3><?php echo number_format($queue_stats->completed ?? 0); ?></h3>
                <p><?php _e('Completed', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card failed">
            <div class="stat-icon">❌</div>
            <div class="stat-content">
                <h3><?php echo number_format($queue_stats->failed ?? 0); ?></h3>
                <p><?php _e('Failed', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card">
            <div class="stat-icon">📄</div>
            <div class="stat-content">
                <h3><?php echo number_format($template_count); ?></h3>
                <p><?php _e('Templates', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="spga-quick-actions">
        <h2><?php _e('Quick Actions', 'seo-page-generator-ai'); ?></h2>
        <div class="action-buttons">
            <a href="<?php echo admin_url('admin.php?page=spga-workflow'); ?>" class="button button-primary button-hero">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php _e('Start New Workflow', 'seo-page-generator-ai'); ?>
            </a>
            
            <a href="<?php echo admin_url('admin.php?page=spga-queue'); ?>" class="button button-secondary">
                <span class="dashicons dashicons-list-view"></span>
                <?php _e('Manage Queue', 'seo-page-generator-ai'); ?>
            </a>
            
            <a href="<?php echo admin_url('admin.php?page=spga-templates'); ?>" class="button button-secondary">
                <span class="dashicons dashicons-admin-page"></span>
                <?php _e('Manage Templates', 'seo-page-generator-ai'); ?>
            </a>
            
            <a href="<?php echo admin_url('admin.php?page=spga-keywords'); ?>" class="button button-secondary">
                <span class="dashicons dashicons-tag"></span>
                <?php _e('Keywords & Locations', 'seo-page-generator-ai'); ?>
            </a>
        </div>
    </div>
    
    <!-- Workflow Overview -->
    <div class="spga-workflow-overview">
        <h2><?php _e('How It Works', 'seo-page-generator-ai'); ?></h2>
        <div class="workflow-steps">
            <div class="workflow-step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h3><?php _e('Input Keywords & Locations', 'seo-page-generator-ai'); ?></h3>
                    <p><?php _e('Upload CSV or manually enter your target keywords and locations for content generation.', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h3><?php _e('Select Template', 'seo-page-generator-ai'); ?></h3>
                    <p><?php _e('Choose from pre-built templates or create custom templates with AI-powered sections.', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h3><?php _e('AI Content Generation', 'seo-page-generator-ai'); ?></h3>
                    <p><?php _e('Our AI generates unique, SEO-optimized content for each keyword-location combination.', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h3><?php _e('SEO Optimization', 'seo-page-generator-ai'); ?></h3>
                    <p><?php _e('Automatic focus keyword assignment, meta titles, descriptions, and internal linking.', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">5</div>
                <div class="step-content">
                    <h3><?php _e('Publish & Track', 'seo-page-generator-ai'); ?></h3>
                    <p><?php _e('Content is published automatically or saved as drafts, with full progress tracking.', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="spga-recent-activity">
        <h2><?php _e('Recent Activity', 'seo-page-generator-ai'); ?></h2>
        
        <?php if (empty($recent_items)): ?>
            <div class="no-activity">
                <p><?php _e('No recent activity. Start by creating your first content workflow!', 'seo-page-generator-ai'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=spga-workflow'); ?>" class="button button-primary">
                    <?php _e('Get Started', 'seo-page-generator-ai'); ?>
                </a>
            </div>
        <?php else: ?>
            <div class="activity-table-wrapper">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Keyword', 'seo-page-generator-ai'); ?></th>
                            <th><?php _e('Location', 'seo-page-generator-ai'); ?></th>
                            <th><?php _e('Status', 'seo-page-generator-ai'); ?></th>
                            <th><?php _e('Created', 'seo-page-generator-ai'); ?></th>
                            <th><?php _e('Processed', 'seo-page-generator-ai'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_items as $item): ?>
                        <tr>
                            <td><strong><?php echo esc_html($item->keyword); ?></strong></td>
                            <td><?php echo esc_html($item->location); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo esc_attr($item->status); ?>">
                                    <?php echo esc_html(ucfirst($item->status)); ?>
                                </span>
                            </td>
                            <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($item->created_at))); ?></td>
                            <td>
                                <?php if ($item->processed_at): ?>
                                    <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($item->processed_at))); ?>
                                <?php else: ?>
                                    <span class="text-muted">—</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="activity-footer">
                <a href="<?php echo admin_url('admin.php?page=spga-queue'); ?>" class="button">
                    <?php _e('View All Items', 'seo-page-generator-ai'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- System Status -->
    <div class="spga-system-status">
        <h2><?php _e('System Status', 'seo-page-generator-ai'); ?></h2>
        <div class="status-grid">
            <div class="status-item">
                <span class="status-label"><?php _e('AI Provider:', 'seo-page-generator-ai'); ?></span>
                <span class="status-value">
                    <?php 
                    $provider = get_option('spga_ai_provider', 'gemini');
                    echo esc_html(ucfirst($provider));
                    ?>
                </span>
                <span class="status-indicator <?php echo $api_configured ? 'status-ok' : 'status-error'; ?>"></span>
            </div>
            
            <div class="status-item">
                <span class="status-label"><?php _e('Queue Processing:', 'seo-page-generator-ai'); ?></span>
                <span class="status-value">
                    <?php 
                    $paused = get_option('spga_queue_paused', false);
                    echo $paused ? __('Paused', 'seo-page-generator-ai') : __('Active', 'seo-page-generator-ai');
                    ?>
                </span>
                <span class="status-indicator <?php echo !$paused ? 'status-ok' : 'status-warning'; ?>"></span>
            </div>
            
            <div class="status-item">
                <span class="status-label"><?php _e('Cron Jobs:', 'seo-page-generator-ai'); ?></span>
                <span class="status-value">
                    <?php 
                    $next_cron = wp_next_scheduled('spga_process_queue');
                    echo $next_cron ? __('Scheduled', 'seo-page-generator-ai') : __('Not Scheduled', 'seo-page-generator-ai');
                    ?>
                </span>
                <span class="status-indicator <?php echo $next_cron ? 'status-ok' : 'status-error'; ?>"></span>
            </div>
            
            <div class="status-item">
                <span class="status-label"><?php _e('Database:', 'seo-page-generator-ai'); ?></span>
                <span class="status-value"><?php _e('Connected', 'seo-page-generator-ai'); ?></span>
                <span class="status-indicator status-ok"></span>
            </div>
        </div>
    </div>
</div>

<style>
.spga-dashboard {
    max-width: 1200px;
}

.spga-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.spga-stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.spga-stat-card.pending { border-left: 4px solid #f39c12; }
.spga-stat-card.processing { border-left: 4px solid #3498db; }
.spga-stat-card.completed { border-left: 4px solid #27ae60; }
.spga-stat-card.failed { border-left: 4px solid #e74c3c; }

.stat-icon {
    font-size: 24px;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 14px;
}

.spga-quick-actions {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.spga-workflow-overview {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.workflow-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.workflow-step {
    text-align: center;
    padding: 15px;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #0073aa;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 15px;
}

.workflow-step h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.workflow-step p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.spga-recent-activity,
.spga-system-status {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.no-activity {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-processing { background: #d1ecf1; color: #0c5460; }
.status-completed { background: #d4edda; color: #155724; }
.status-failed { background: #f8d7da; color: #721c24; }
.status-paused { background: #e2e3e5; color: #383d41; }

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.status-label {
    font-weight: bold;
    min-width: 120px;
}

.status-value {
    flex: 1;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-ok { background: #28a745; }
.status-warning { background: #ffc107; }
.status-error { background: #dc3545; }

.activity-footer {
    text-align: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}
</style>
