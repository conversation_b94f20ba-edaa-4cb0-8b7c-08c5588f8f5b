<?php
/**
 * Keywords & Locations Management view for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle form submissions
if (isset($_POST['action'])) {
    check_admin_referer('spga_keywords_action', 'spga_keywords_nonce');
    
    $action = sanitize_text_field($_POST['action']);
    
    switch ($action) {
        case 'add_keyword':
            $keyword = sanitize_text_field($_POST['keyword']);
            $location = sanitize_text_field($_POST['location']);
            $priority = intval($_POST['priority']);
            
            global $wpdb;
            $keywords_table = $wpdb->prefix . 'spga_keywords';
            
            $result = $wpdb->insert(
                $keywords_table,
                array(
                    'keyword' => $keyword,
                    'location' => $location,
                    'priority' => $priority,
                    'is_active' => 1
                ),
                array('%s', '%s', '%d', '%d')
            );
            
            if ($result) {
                echo '<div class="notice notice-success"><p>' . __('Keyword-location combination added successfully!', 'seo-page-generator-ai') . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . __('Failed to add keyword-location combination.', 'seo-page-generator-ai') . '</p></div>';
            }
            break;
            
        case 'bulk_add_keywords':
            $keywords = array_filter(array_map('trim', explode("\n", sanitize_textarea_field($_POST['bulk_keywords']))));
            $locations = array_filter(array_map('trim', explode("\n", sanitize_textarea_field($_POST['bulk_locations']))));
            
            if (!empty($keywords) && !empty($locations)) {
                global $wpdb;
                $keywords_table = $wpdb->prefix . 'spga_keywords';
                $added = 0;
                
                foreach ($keywords as $keyword) {
                    foreach ($locations as $location) {
                        // Check if combination already exists
                        $exists = $wpdb->get_var($wpdb->prepare(
                            "SELECT COUNT(*) FROM $keywords_table WHERE keyword = %s AND location = %s",
                            $keyword, $location
                        ));
                        
                        if (!$exists) {
                            $result = $wpdb->insert(
                                $keywords_table,
                                array(
                                    'keyword' => $keyword,
                                    'location' => $location,
                                    'priority' => 0,
                                    'is_active' => 1
                                ),
                                array('%s', '%s', '%d', '%d')
                            );
                            
                            if ($result) {
                                $added++;
                            }
                        }
                    }
                }
                
                echo '<div class="notice notice-success"><p>' . sprintf(__('Added %d keyword-location combinations!', 'seo-page-generator-ai'), $added) . '</p></div>';
            }
            break;
            
        case 'delete_keywords':
            if (isset($_POST['keyword_ids']) && is_array($_POST['keyword_ids'])) {
                global $wpdb;
                $keywords_table = $wpdb->prefix . 'spga_keywords';
                $ids = array_map('intval', $_POST['keyword_ids']);
                $placeholders = implode(',', array_fill(0, count($ids), '%d'));
                
                $deleted = $wpdb->query($wpdb->prepare("DELETE FROM $keywords_table WHERE id IN ($placeholders)", $ids));
                
                echo '<div class="notice notice-success"><p>' . sprintf(__('Deleted %d keyword combinations.', 'seo-page-generator-ai'), $deleted) . '</p></div>';
            }
            break;
    }
}

// Get filter parameters
$search = sanitize_text_field($_GET['s'] ?? '');
$per_page = 50;
$paged = max(1, intval($_GET['paged'] ?? 1));
$offset = ($paged - 1) * $per_page;

// Build query
global $wpdb;
$keywords_table = $wpdb->prefix . 'spga_keywords';

$where_conditions = array('1=1');
$query_params = array();

if (!empty($search)) {
    $where_conditions[] = '(keyword LIKE %s OR location LIKE %s)';
    $query_params[] = '%' . $wpdb->esc_like($search) . '%';
    $query_params[] = '%' . $wpdb->esc_like($search) . '%';
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$total_query = "SELECT COUNT(*) FROM $keywords_table WHERE $where_clause";
$total_items = $wpdb->get_var($wpdb->prepare($total_query, $query_params));

// Get items for current page
$items_query = "SELECT * FROM $keywords_table WHERE $where_clause ORDER BY priority DESC, keyword ASC, location ASC LIMIT %d OFFSET %d";
$query_params[] = $per_page;
$query_params[] = $offset;

$keyword_items = $wpdb->get_results($wpdb->prepare($items_query, $query_params));

// Calculate pagination
$total_pages = ceil($total_items / $per_page);

// Get statistics
$stats = $wpdb->get_row("
    SELECT 
        COUNT(*) as total,
        COUNT(DISTINCT keyword) as unique_keywords,
        COUNT(DISTINCT location) as unique_locations,
        SUM(usage_count) as total_usage
    FROM $keywords_table
");
?>

<div class="wrap spga-keywords">
    <h1 class="wp-heading-inline"><?php _e('Keywords & Locations', 'seo-page-generator-ai'); ?></h1>
    
    <hr class="wp-header-end">
    
    <!-- Statistics Cards -->
    <div class="spga-stats-grid">
        <div class="spga-stat-card">
            <div class="stat-icon">🔑</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->total); ?></h3>
                <p><?php _e('Total Combinations', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->unique_keywords); ?></h3>
                <p><?php _e('Unique Keywords', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card">
            <div class="stat-icon">📍</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->unique_locations); ?></h3>
                <p><?php _e('Unique Locations', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
        
        <div class="spga-stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
                <h3><?php echo number_format($stats->total_usage); ?></h3>
                <p><?php _e('Total Usage', 'seo-page-generator-ai'); ?></p>
            </div>
        </div>
    </div>
    
    <!-- Add Keywords Section -->
    <div class="spga-add-keywords">
        <h2><?php _e('Add Keywords & Locations', 'seo-page-generator-ai'); ?></h2>
        
        <div class="keyword-input-methods">
            <div class="method-tabs">
                <button type="button" class="tab-button active" data-tab="single">
                    <?php _e('Single Entry', 'seo-page-generator-ai'); ?>
                </button>
                <button type="button" class="tab-button" data-tab="bulk">
                    <?php _e('Bulk Entry', 'seo-page-generator-ai'); ?>
                </button>
                <button type="button" class="tab-button" data-tab="csv">
                    <?php _e('CSV Import', 'seo-page-generator-ai'); ?>
                </button>
            </div>
            
            <!-- Single Entry Tab -->
            <div class="tab-content active" id="single-tab">
                <form method="post">
                    <?php wp_nonce_field('spga_keywords_action', 'spga_keywords_nonce'); ?>
                    <input type="hidden" name="action" value="add_keyword">
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="keyword"><?php _e('Keyword', 'seo-page-generator-ai'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="keyword" name="keyword" class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="location"><?php _e('Location', 'seo-page-generator-ai'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="location" name="location" class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="priority"><?php _e('Priority', 'seo-page-generator-ai'); ?></label>
                            </th>
                            <td>
                                <input type="number" id="priority" name="priority" value="0" min="0" max="10">
                                <p class="description"><?php _e('Higher priority items are processed first (0-10).', 'seo-page-generator-ai'); ?></p>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" class="button button-primary" value="<?php esc_attr_e('Add Keyword-Location', 'seo-page-generator-ai'); ?>">
                    </p>
                </form>
            </div>
            
            <!-- Bulk Entry Tab -->
            <div class="tab-content" id="bulk-tab">
                <form method="post">
                    <?php wp_nonce_field('spga_keywords_action', 'spga_keywords_nonce'); ?>
                    <input type="hidden" name="action" value="bulk_add_keywords">
                    
                    <div class="bulk-input-grid">
                        <div class="bulk-column">
                            <label for="bulk_keywords">
                                <strong><?php _e('Keywords', 'seo-page-generator-ai'); ?></strong>
                                <span class="description"><?php _e('Enter one keyword per line', 'seo-page-generator-ai'); ?></span>
                            </label>
                            <textarea id="bulk_keywords" name="bulk_keywords" rows="10" placeholder="<?php esc_attr_e('Debt Help\nDebt Advice\nWrite Off Debt\nHelp With Debt', 'seo-page-generator-ai'); ?>"></textarea>
                        </div>
                        
                        <div class="bulk-column">
                            <label for="bulk_locations">
                                <strong><?php _e('Locations', 'seo-page-generator-ai'); ?></strong>
                                <span class="description"><?php _e('Enter one location per line', 'seo-page-generator-ai'); ?></span>
                            </label>
                            <textarea id="bulk_locations" name="bulk_locations" rows="10" placeholder="<?php esc_attr_e('London\nManchester\nBirmingham\nLiverpool', 'seo-page-generator-ai'); ?>"></textarea>
                        </div>
                    </div>
                    
                    <div class="combination-preview">
                        <h4><?php _e('Preview Combinations', 'seo-page-generator-ai'); ?></h4>
                        <div id="bulk-combinations-list">
                            <p class="text-muted"><?php _e('Enter keywords and locations above to see the combinations that will be created.', 'seo-page-generator-ai'); ?></p>
                        </div>
                    </div>
                    
                    <p class="submit">
                        <input type="submit" class="button button-primary" value="<?php esc_attr_e('Add All Combinations', 'seo-page-generator-ai'); ?>">
                    </p>
                </form>
            </div>
            
            <!-- CSV Import Tab -->
            <div class="tab-content" id="csv-tab">
                <div class="csv-import-section">
                    <h4><?php _e('CSV Import', 'seo-page-generator-ai'); ?></h4>
                    <p><?php _e('Upload a CSV file with columns: keyword, location, priority (optional)', 'seo-page-generator-ai'); ?></p>
                    
                    <div class="csv-upload-area">
                        <input type="file" id="csv-file-input" accept=".csv" style="display: none;">
                        <button type="button" class="button" onclick="document.getElementById('csv-file-input').click();">
                            <?php _e('Choose CSV File', 'seo-page-generator-ai'); ?>
                        </button>
                        <span id="csv-file-name"></span>
                    </div>
                    
                    <div class="csv-template">
                        <h4><?php _e('CSV Template', 'seo-page-generator-ai'); ?></h4>
                        <p><?php _e('Download a template CSV file:', 'seo-page-generator-ai'); ?></p>
                        <button type="button" class="button button-secondary" id="download-keywords-csv-template">
                            <?php _e('Download Template', 'seo-page-generator-ai'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Keywords List -->
    <div class="spga-keywords-list">
        <h2><?php _e('Existing Keywords & Locations', 'seo-page-generator-ai'); ?></h2>
        
        <!-- Search and Filters -->
        <div class="tablenav top">
            <div class="alignleft actions">
                <form method="get">
                    <input type="hidden" name="page" value="spga-keywords">
                    <input type="search" name="s" value="<?php echo esc_attr($search); ?>" placeholder="<?php esc_attr_e('Search keywords or locations...', 'seo-page-generator-ai'); ?>">
                    <input type="submit" class="button" value="<?php esc_attr_e('Search', 'seo-page-generator-ai'); ?>">
                </form>
            </div>
            
            <div class="tablenav-pages">
                <?php
                if ($total_pages > 1) {
                    $page_links = paginate_links(array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => __('&laquo;'),
                        'next_text' => __('&raquo;'),
                        'total' => $total_pages,
                        'current' => $paged
                    ));
                    
                    if ($page_links) {
                        echo '<span class="displaying-num">' . sprintf(_n('%s item', '%s items', $total_items), number_format_i18n($total_items)) . '</span>';
                        echo $page_links;
                    }
                }
                ?>
            </div>
        </div>
        
        <!-- Keywords Table -->
        <form method="post">
            <?php wp_nonce_field('spga_keywords_action', 'spga_keywords_nonce'); ?>
            <input type="hidden" name="action" value="delete_keywords">
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <td class="manage-column column-cb check-column">
                            <input type="checkbox" id="cb-select-all-keywords">
                        </td>
                        <th><?php _e('Keyword', 'seo-page-generator-ai'); ?></th>
                        <th><?php _e('Location', 'seo-page-generator-ai'); ?></th>
                        <th><?php _e('Priority', 'seo-page-generator-ai'); ?></th>
                        <th><?php _e('Usage Count', 'seo-page-generator-ai'); ?></th>
                        <th><?php _e('Last Used', 'seo-page-generator-ai'); ?></th>
                        <th><?php _e('Status', 'seo-page-generator-ai'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($keyword_items)): ?>
                    <tr>
                        <td colspan="7" class="no-items">
                            <?php _e('No keyword combinations found.', 'seo-page-generator-ai'); ?>
                        </td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($keyword_items as $item): ?>
                        <tr>
                            <th scope="row" class="check-column">
                                <input type="checkbox" name="keyword_ids[]" value="<?php echo $item->id; ?>">
                            </th>
                            <td><strong><?php echo esc_html($item->keyword); ?></strong></td>
                            <td><?php echo esc_html($item->location); ?></td>
                            <td>
                                <span class="priority-badge priority-<?php echo $item->priority; ?>">
                                    <?php echo $item->priority; ?>
                                </span>
                            </td>
                            <td><?php echo number_format($item->usage_count); ?></td>
                            <td>
                                <?php if ($item->last_used): ?>
                                    <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($item->last_used))); ?>
                                <?php else: ?>
                                    <span class="text-muted">—</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $item->is_active ? 'active' : 'inactive'; ?>">
                                    <?php echo $item->is_active ? __('Active', 'seo-page-generator-ai') : __('Inactive', 'seo-page-generator-ai'); ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <div class="tablenav bottom">
                <div class="alignleft actions bulkactions">
                    <select name="bulk_action">
                        <option value="-1"><?php _e('Bulk Actions', 'seo-page-generator-ai'); ?></option>
                        <option value="delete"><?php _e('Delete', 'seo-page-generator-ai'); ?></option>
                    </select>
                    <input type="submit" class="button action" value="<?php esc_attr_e('Apply', 'seo-page-generator-ai'); ?>" onclick="return confirm('<?php esc_attr_e('Are you sure you want to delete the selected items?', 'seo-page-generator-ai'); ?>');">
                </div>
            </div>
        </form>
    </div>
</div>

<style>
.bulk-input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 20px;
}

.bulk-column label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.bulk-column .description {
    display: block;
    font-weight: normal;
    color: #666;
    font-size: 13px;
    margin-top: 5px;
}

.bulk-column textarea {
    width: 100%;
    min-height: 200px;
    font-family: monospace;
}

.method-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.tab-button {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
}

.tab-button.active {
    border-bottom-color: #0073aa;
    color: #0073aa;
    font-weight: bold;
}

.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
}

.priority-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

.priority-0 { background: #e9ecef; color: #6c757d; }
.priority-1, .priority-2 { background: #fff3cd; color: #856404; }
.priority-3, .priority-4, .priority-5 { background: #d1ecf1; color: #0c5460; }
.priority-6, .priority-7, .priority-8 { background: #d4edda; color: #155724; }
.priority-9, .priority-10 { background: #f8d7da; color: #721c24; }

.status-active { background: #d4edda; color: #155724; }
.status-inactive { background: #f8d7da; color: #721c24; }
</style>

<script>
jQuery(document).ready(function($) {
    // Tab switching
    $('.tab-button').on('click', function() {
        var tab = $(this).data('tab');
        
        $('.tab-button').removeClass('active');
        $(this).addClass('active');
        
        $('.tab-content').removeClass('active');
        $('#' + tab + '-tab').addClass('active');
    });
    
    // Real-time combination preview for bulk entry
    $('#bulk_keywords, #bulk_locations').on('input', function() {
        updateBulkCombinationPreview();
    });
    
    function updateBulkCombinationPreview() {
        const keywords = $('#bulk_keywords').val().split('\n').filter(k => k.trim());
        const locations = $('#bulk_locations').val().split('\n').filter(l => l.trim());
        
        if (keywords.length && locations.length) {
            const combinations = [];
            keywords.forEach(keyword => {
                locations.forEach(location => {
                    combinations.push(`${keyword.trim()} - ${location.trim()}`);
                });
            });
            
            const preview = combinations.slice(0, 20).map(combo => 
                `<span class="combination-item">${combo}</span>`
            ).join('');
            
            const total = combinations.length;
            const showing = Math.min(20, total);
            
            $('#bulk-combinations-list').html(`
                <div class="combinations-preview">${preview}</div>
                <p class="combinations-count">
                    Showing ${showing} of ${total} combinations
                    ${total > 20 ? `<span class="text-muted">(${total - 20} more...)</span>` : ''}
                </p>
            `);
        } else {
            $('#bulk-combinations-list').html('<p class="text-muted"><?php _e('Enter keywords and locations above to see the combinations that will be created.', 'seo-page-generator-ai'); ?></p>');
        }
    }
    
    // CSV file selection
    $('#csv-file-input').on('change', function() {
        var fileName = $(this)[0].files[0]?.name || '';
        $('#csv-file-name').text(fileName);
    });
    
    // Download CSV template
    $('#download-keywords-csv-template').on('click', function() {
        var csvContent = "keyword,location,priority\n";
        csvContent += "Debt Help,London,1\n";
        csvContent += "Debt Advice,Manchester,1\n";
        csvContent += "Write Off Debt,Birmingham,2\n";
        
        var blob = new Blob([csvContent], { type: 'text/csv' });
        var url = window.URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = 'keywords-template.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    });
    
    // Select all checkbox
    $('#cb-select-all-keywords').on('change', function() {
        $('input[name="keyword_ids[]"]').prop('checked', $(this).prop('checked'));
    });
});
</script>
