<?php
/**
 * Templates Management view for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle form submissions
if (isset($_POST['action'])) {
    check_admin_referer('spga_template_action', 'spga_template_nonce');
    
    $action = sanitize_text_field($_POST['action']);
    
    switch ($action) {
        case 'create_template':
            $template_data = array(
                'name' => sanitize_text_field($_POST['template_name']),
                'description' => sanitize_textarea_field($_POST['template_description']),
                'content' => wp_kses_post($_POST['template_content']),
                'variables' => array(),
                'post_type' => sanitize_text_field($_POST['post_type']),
                'category_id' => intval($_POST['category_id']),
                'tags' => sanitize_text_field($_POST['tags']),
                'is_default' => intval($_POST['is_default'] ?? 0)
            );
            
            $template_id = spga()->template_engine->create_template($template_data);
            
            if ($template_id) {
                echo '<div class="notice notice-success"><p>' . __('Template created successfully!', 'seo-page-generator-ai') . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . __('Failed to create template.', 'seo-page-generator-ai') . '</p></div>';
            }
            break;
            
        case 'update_template':
            $template_id = intval($_POST['template_id']);
            $template_data = array(
                'name' => sanitize_text_field($_POST['template_name']),
                'description' => sanitize_textarea_field($_POST['template_description']),
                'content' => wp_kses_post($_POST['template_content']),
                'variables' => array(),
                'post_type' => sanitize_text_field($_POST['post_type']),
                'category_id' => intval($_POST['category_id']),
                'tags' => sanitize_text_field($_POST['tags']),
                'is_default' => intval($_POST['is_default'] ?? 0)
            );
            
            $result = spga()->template_engine->update_template($template_id, $template_data);
            
            if ($result) {
                echo '<div class="notice notice-success"><p>' . __('Template updated successfully!', 'seo-page-generator-ai') . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . __('Failed to update template.', 'seo-page-generator-ai') . '</p></div>';
            }
            break;
            
        case 'delete_template':
            $template_id = intval($_POST['template_id']);
            $result = spga()->template_engine->delete_template($template_id);
            
            if ($result) {
                echo '<div class="notice notice-success"><p>' . __('Template deleted successfully!', 'seo-page-generator-ai') . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . __('Failed to delete template.', 'seo-page-generator-ai') . '</p></div>';
            }
            break;
    }
}

// Get current action
$current_action = sanitize_text_field($_GET['action'] ?? 'list');
$template_id = intval($_GET['template_id'] ?? 0);

// Get templates
$templates = spga()->template_engine->get_templates();

// Get categories for dropdown
$categories = get_categories(array('hide_empty' => false));
?>

<div class="spga-admin-page">
    <div class="wrap spga-templates">
        <h1><?php _e('Template Management', 'seo-page-generator-ai'); ?></h1>
    
    <?php if ($current_action === 'list'): ?>
        <a href="<?php echo admin_url('admin.php?page=spga-templates&action=new'); ?>" class="page-title-action">
            <?php _e('Add New Template', 'seo-page-generator-ai'); ?>
        </a>
    <?php endif; ?>
    
    <hr class="wp-header-end">
    
    <?php if ($current_action === 'list'): ?>
        <!-- Templates List -->
        <div class="spga-templates-grid">
            <?php if (empty($templates)): ?>
                <div class="no-templates">
                    <h3><?php _e('No Templates Found', 'seo-page-generator-ai'); ?></h3>
                    <p><?php _e('Create your first template to start generating content.', 'seo-page-generator-ai'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=spga-templates&action=new'); ?>" class="button button-primary">
                        <?php _e('Create Template', 'seo-page-generator-ai'); ?>
                    </a>
                </div>
            <?php else: ?>
                <?php foreach ($templates as $template): ?>
                <div class="template-card">
                    <div class="template-header">
                        <h3><?php echo esc_html($template->name); ?></h3>
                        <?php if ($template->is_default): ?>
                            <span class="default-badge"><?php _e('Default', 'seo-page-generator-ai'); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="template-meta">
                        <span class="post-type"><?php echo esc_html(ucfirst($template->post_type)); ?></span>
                        <span class="usage-count"><?php echo sprintf(__('Used %d times', 'seo-page-generator-ai'), $template->usage_count); ?></span>
                    </div>
                    
                    <div class="template-description">
                        <?php echo esc_html($template->description); ?>
                    </div>
                    
                    <div class="template-actions">
                        <a href="<?php echo admin_url('admin.php?page=spga-templates&action=edit&template_id=' . $template->id); ?>" class="button">
                            <?php _e('Edit', 'seo-page-generator-ai'); ?>
                        </a>
                        <a href="#" class="button preview-template" data-template-id="<?php echo $template->id; ?>">
                            <?php _e('Preview', 'seo-page-generator-ai'); ?>
                        </a>
                        <a href="<?php echo admin_url('admin.php?page=spga-templates&action=duplicate&template_id=' . $template->id); ?>" class="button">
                            <?php _e('Duplicate', 'seo-page-generator-ai'); ?>
                        </a>
                        <?php if (!$template->is_default): ?>
                        <a href="#" class="button delete-template" data-template-id="<?php echo $template->id; ?>">
                            <?php _e('Delete', 'seo-page-generator-ai'); ?>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
    <?php elseif ($current_action === 'new' || $current_action === 'edit'): ?>
        <!-- Template Editor -->
        <?php
        $template = null;
        if ($current_action === 'edit' && $template_id) {
            $template = spga()->template_engine->get_template($template_id);
        }
        ?>
        
        <div class="template-editor">
            <form method="post">
                <?php wp_nonce_field('spga_template_action', 'spga_template_nonce'); ?>
                <input type="hidden" name="action" value="<?php echo $current_action === 'edit' ? 'update_template' : 'create_template'; ?>">
                <?php if ($template): ?>
                    <input type="hidden" name="template_id" value="<?php echo $template->id; ?>">
                <?php endif; ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="template_name"><?php _e('Template Name', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="template_name" name="template_name" value="<?php echo esc_attr($template->name ?? ''); ?>" class="regular-text" required>
                            <p class="description"><?php _e('A descriptive name for this template.', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="template_description"><?php _e('Description', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <textarea id="template_description" name="template_description" rows="3" class="large-text"><?php echo esc_textarea($template->description ?? ''); ?></textarea>
                            <p class="description"><?php _e('Brief description of what this template is used for.', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="post_type"><?php _e('Post Type', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <select id="post_type" name="post_type">
                                <option value="post" <?php selected($template->post_type ?? 'post', 'post'); ?>><?php _e('Posts', 'seo-page-generator-ai'); ?></option>
                                <option value="page" <?php selected($template->post_type ?? '', 'page'); ?>><?php _e('Pages', 'seo-page-generator-ai'); ?></option>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="category_id"><?php _e('Default Category', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <select id="category_id" name="category_id">
                                <option value="0"><?php _e('No Category', 'seo-page-generator-ai'); ?></option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category->term_id; ?>" <?php selected($template->category_id ?? 0, $category->term_id); ?>>
                                        <?php echo esc_html($category->name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Default category for posts created with this template.', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="tags"><?php _e('Default Tags', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="tags" name="tags" value="<?php echo esc_attr($template->tags ?? ''); ?>" class="regular-text">
                            <p class="description"><?php _e('Comma-separated list of default tags.', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="is_default"><?php _e('Default Template', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="is_default" name="is_default" value="1" <?php checked($template->is_default ?? 0, 1); ?>>
                            <label for="is_default"><?php _e('Use this as the default template', 'seo-page-generator-ai'); ?></label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="template_content"><?php _e('Template Content', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <div class="template-editor-wrapper">
                                <div class="editor-toolbar">
                                    <button type="button" class="button insert-shortcode" data-shortcode="[spga_dynamic name='h1']">
                                        <?php _e('Insert H1', 'seo-page-generator-ai'); ?>
                                    </button>
                                    <button type="button" class="button insert-shortcode" data-shortcode="[spga_dynamic name='h2']">
                                        <?php _e('Insert H2', 'seo-page-generator-ai'); ?>
                                    </button>
                                    <button type="button" class="button insert-shortcode" data-shortcode="[spga_dynamic name='content']">
                                        <?php _e('Insert Content', 'seo-page-generator-ai'); ?>
                                    </button>
                                    <button type="button" class="button insert-variable" data-variable="[keyword]">
                                        <?php _e('Insert Keyword', 'seo-page-generator-ai'); ?>
                                    </button>
                                    <button type="button" class="button insert-variable" data-variable="[location]">
                                        <?php _e('Insert Location', 'seo-page-generator-ai'); ?>
                                    </button>
                                </div>
                                
                                <textarea id="template_content" name="template_content" rows="20" class="large-text code"><?php echo esc_textarea($template->content ?? ''); ?></textarea>
                                
                                <div class="template-help">
                                    <h4><?php _e('Available Shortcodes:', 'seo-page-generator-ai'); ?></h4>
                                    <ul>
                                        <li><code>[spga_dynamic name="section_name"]</code> - <?php _e('AI-generated content section', 'seo-page-generator-ai'); ?></li>
                                        <li><code>[spga_variable name="keyword"]</code> - <?php _e('Insert keyword variable', 'seo-page-generator-ai'); ?></li>
                                        <li><code>[keyword]</code> - <?php _e('Direct keyword replacement', 'seo-page-generator-ai'); ?></li>
                                        <li><code>[location]</code> - <?php _e('Direct location replacement', 'seo-page-generator-ai'); ?></li>
                                        <li><code>[title]</code> - <?php _e('Generated page title', 'seo-page-generator-ai'); ?></li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
                
                <div class="template-actions">
                    <input type="submit" class="button button-primary" value="<?php echo $current_action === 'edit' ? __('Update Template', 'seo-page-generator-ai') : __('Create Template', 'seo-page-generator-ai'); ?>">
                    <a href="<?php echo admin_url('admin.php?page=spga-templates'); ?>" class="button">
                        <?php _e('Cancel', 'seo-page-generator-ai'); ?>
                    </a>
                    <button type="button" class="button preview-current-template">
                        <?php _e('Preview', 'seo-page-generator-ai'); ?>
                    </button>
                </div>
            </form>
        </div>
        
    <?php endif; ?>
</div>

<!-- Template Preview Modal -->
<div id="template-preview-modal" class="spga-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><?php _e('Template Preview', 'seo-page-generator-ai'); ?></h3>
            <button type="button" class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div id="template-preview-content">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.spga-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.template-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.template-header h3 {
    margin: 0;
    color: #23282d;
}

.default-badge {
    background: #46b450;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 12px;
    color: #666;
}

.template-description {
    margin-bottom: 20px;
    color: #555;
    line-height: 1.4;
}

.template-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.template-editor-wrapper {
    position: relative;
}

.editor-toolbar {
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
}

.editor-toolbar .button {
    margin-right: 5px;
    margin-bottom: 5px;
}

.template-help {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.template-help h4 {
    margin: 0 0 10px 0;
}

.template-help ul {
    margin: 0;
    padding-left: 20px;
}

.template-help li {
    margin-bottom: 5px;
}

.template-help code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
}

.no-templates {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.spga-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
}

.modal-header h3 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Insert shortcode buttons
    $('.insert-shortcode').on('click', function() {
        var shortcode = $(this).data('shortcode');
        insertAtCursor($('#template_content')[0], shortcode);
    });
    
    $('.insert-variable').on('click', function() {
        var variable = $(this).data('variable');
        insertAtCursor($('#template_content')[0], variable);
    });
    
    // Preview template
    $('.preview-template').on('click', function(e) {
        e.preventDefault();
        var templateId = $(this).data('template-id');
        previewTemplate(templateId);
    });
    
    $('.preview-current-template').on('click', function(e) {
        e.preventDefault();
        var content = $('#template_content').val();
        previewCurrentTemplate(content);
    });
    
    // Delete template
    $('.delete-template').on('click', function(e) {
        e.preventDefault();
        if (confirm('<?php _e('Are you sure you want to delete this template?', 'seo-page-generator-ai'); ?>')) {
            var templateId = $(this).data('template-id');
            deleteTemplate(templateId);
        }
    });
    
    // Modal close
    $('.modal-close, .spga-modal').on('click', function(e) {
        if (e.target === this) {
            $('.spga-modal').hide();
        }
    });
    
    function insertAtCursor(textarea, text) {
        var startPos = textarea.selectionStart;
        var endPos = textarea.selectionEnd;
        var value = textarea.value;
        
        textarea.value = value.substring(0, startPos) + text + value.substring(endPos, value.length);
        textarea.selectionStart = textarea.selectionEnd = startPos + text.length;
        textarea.focus();
    }
    
    function previewTemplate(templateId) {
        $.post(ajaxurl, {
            action: 'spga_preview_template',
            template_id: templateId,
            nonce: spga_ajax.nonce
        }, function(response) {
            if (response.success) {
                $('#template-preview-content').html(response.data);
                $('#template-preview-modal').show();
            }
        });
    }
    
    function previewCurrentTemplate(content) {
        $.post(ajaxurl, {
            action: 'spga_preview_template_content',
            content: content,
            nonce: spga_ajax.nonce
        }, function(response) {
            if (response.success) {
                $('#template-preview-content').html(response.data);
                $('#template-preview-modal').show();
            }
        });
    }
    
    function deleteTemplate(templateId) {
        var form = $('<form method="post">')
            .append('<?php wp_nonce_field('spga_template_action', 'spga_template_nonce', true, false); ?>')
            .append('<input type="hidden" name="action" value="delete_template">')
            .append('<input type="hidden" name="template_id" value="' + templateId + '">');
        
        $('body').append(form);
        form.submit();
    }
});
</script>
    </div>
</div>
