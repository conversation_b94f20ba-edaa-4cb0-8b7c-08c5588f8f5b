<?php
/**
 * Enhanced Workflow view for SEO Page Generator AI
 * Step-by-step onboarding-style interface
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get available templates
global $wpdb;
$templates_table = $wpdb->prefix . 'spga_templates';
$templates = $wpdb->get_results("SELECT * FROM $templates_table WHERE is_active = 1 ORDER BY is_default DESC, name ASC");

// Check if API is configured
$gemini_key = get_option('spga_gemini_api_key');
$openai_key = get_option('spga_openai_api_key');
$api_configured = !empty($gemini_key) || !empty($openai_key);
?>

<div class="spga-admin-page">
    <div class="wrap spga-workflow">
        <h1><?php _e('Content Generation Workflow', 'seo-page-generator-ai'); ?></h1>
    <p class="description">
        <?php _e('Follow these simple steps to generate SEO-optimized content in bulk. This guided workflow will help you create multiple pages/posts automatically.', 'seo-page-generator-ai'); ?>
    </p>
    
    <?php if (!$api_configured): ?>
    <div class="notice notice-error">
        <p>
            <strong><?php _e('API Configuration Required:', 'seo-page-generator-ai'); ?></strong>
            <?php printf(
                __('Please configure your AI API keys in <a href="%s">Settings</a> before starting the workflow.', 'seo-page-generator-ai'),
                admin_url('admin.php?page=spga-settings')
            ); ?>
        </p>
    </div>
    <?php endif; ?>
    
    <div class="spga-workflow-container">
        <!-- Progress Indicator -->
        <div class="workflow-progress">
            <div class="progress-steps">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label"><?php _e('Input Data', 'seo-page-generator-ai'); ?></div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label"><?php _e('Template', 'seo-page-generator-ai'); ?></div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label"><?php _e('Settings', 'seo-page-generator-ai'); ?></div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-label"><?php _e('Review', 'seo-page-generator-ai'); ?></div>
                </div>
                <div class="step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-label"><?php _e('Generate', 'seo-page-generator-ai'); ?></div>
                </div>
            </div>
        </div>
        
        <form id="spga-workflow-form" method="post">
            <?php wp_nonce_field('spga_workflow', 'spga_workflow_nonce'); ?>
            
            <!-- Step 1: Input Data -->
            <div class="workflow-step-content" id="step-1">
                <div class="step-header">
                    <h2><?php _e('Step 1: Input Keywords & Locations', 'seo-page-generator-ai'); ?></h2>
                    <p><?php _e('Choose how you want to input your keywords and locations for content generation.', 'seo-page-generator-ai'); ?></p>
                </div>
                
                <div class="input-method-selector">
                    <div class="method-option">
                        <input type="radio" id="method-manual" name="input_method" value="manual" checked>
                        <label for="method-manual" class="method-card">
                            <div class="method-icon">✏️</div>
                            <h3><?php _e('Manual Input', 'seo-page-generator-ai'); ?></h3>
                            <p><?php _e('Enter keywords and locations manually using the form below.', 'seo-page-generator-ai'); ?></p>
                        </label>
                    </div>
                    
                    <div class="method-option">
                        <input type="radio" id="method-csv" name="input_method" value="csv">
                        <label for="method-csv" class="method-card">
                            <div class="method-icon">📄</div>
                            <h3><?php _e('CSV Upload', 'seo-page-generator-ai'); ?></h3>
                            <p><?php _e('Upload a CSV file with your keywords and locations for bulk processing.', 'seo-page-generator-ai'); ?></p>
                        </label>
                    </div>
                </div>
                
                <!-- Manual Input Section -->
                <div id="manual-input-section" class="input-section">
                    <div class="input-grid">
                        <div class="input-column">
                            <div class="input-header">
                                <label>
                                    <strong><?php _e('Keywords', 'seo-page-generator-ai'); ?></strong>
                                    <span class="description"><?php _e('Add keywords one by one or use bulk input', 'seo-page-generator-ai'); ?></span>
                                </label>
                                <button type="button" class="spga-btn spga-btn-secondary" id="toggle-keywords-bulk">
                                    <?php _e('Individual Input', 'seo-page-generator-ai'); ?>
                                </button>
                            </div>

                            <!-- Individual Keyword Inputs -->
                            <div id="keywords-individual" class="individual-inputs" style="display: none;">
                                <div class="input-item">
                                    <input type="text" class="spga-form-control keyword-input" placeholder="<?php esc_attr_e('Enter keyword...', 'seo-page-generator-ai'); ?>" value="Just gaming">
                                    <button type="button" class="spga-btn spga-btn-danger remove-input">×</button>
                                </div>
                                <div class="input-item">
                                    <input type="text" class="spga-form-control keyword-input" placeholder="<?php esc_attr_e('Enter keyword...', 'seo-page-generator-ai'); ?>" value="gaming">
                                    <button type="button" class="spga-btn spga-btn-danger remove-input">×</button>
                                </div>
                                <button type="button" class="spga-btn spga-btn-secondary add-keyword">
                                    + <?php _e('Add Keyword', 'seo-page-generator-ai'); ?>
                                </button>
                            </div>

                            <!-- Bulk Keyword Input -->
                            <div id="keywords-bulk" class="bulk-input">
                                <textarea id="keywords-input" name="keywords" rows="8" class="spga-form-control" placeholder="<?php esc_attr_e('Debt Help\nDebt Advice\nWrite Off Debt\nHelp With Debt', 'seo-page-generator-ai'); ?>"></textarea>
                            </div>
                        </div>

                        <div class="input-column">
                            <div class="input-header">
                                <label>
                                    <strong><?php _e('Locations', 'seo-page-generator-ai'); ?></strong>
                                    <span class="description"><?php _e('Add locations one by one or use bulk input', 'seo-page-generator-ai'); ?></span>
                                </label>
                                <button type="button" class="spga-btn spga-btn-secondary" id="toggle-locations-bulk">
                                    <?php _e('Individual Input', 'seo-page-generator-ai'); ?>
                                </button>
                            </div>

                            <!-- Individual Location Inputs -->
                            <div id="locations-individual" class="individual-inputs" style="display: none;">
                                <div class="input-item">
                                    <input type="text" class="spga-form-control location-input" placeholder="<?php esc_attr_e('Enter location...', 'seo-page-generator-ai'); ?>" value="London">
                                    <button type="button" class="spga-btn spga-btn-danger remove-input">×</button>
                                </div>
                                <div class="input-item">
                                    <input type="text" class="spga-form-control location-input" placeholder="<?php esc_attr_e('Enter location...', 'seo-page-generator-ai'); ?>" value="UK">
                                    <button type="button" class="spga-btn spga-btn-danger remove-input">×</button>
                                </div>
                                <button type="button" class="spga-btn spga-btn-secondary add-location">
                                    + <?php _e('Add Location', 'seo-page-generator-ai'); ?>
                                </button>
                            </div>

                            <!-- Bulk Location Input -->
                            <div id="locations-bulk" class="bulk-input">
                                <textarea id="locations-input" name="locations" rows="8" class="spga-form-control" placeholder="<?php esc_attr_e('London\nManchester\nBirmingham\nLiverpool', 'seo-page-generator-ai'); ?>"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="combination-preview">
                        <h4><?php _e('Preview Combinations', 'seo-page-generator-ai'); ?></h4>
                        <div id="combinations-list">
                            <p class="text-muted"><?php _e('Enter keywords and locations above to see the combinations that will be generated.', 'seo-page-generator-ai'); ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- CSV Upload Section -->
                <div id="csv-input-section" class="input-section" style="display: none;">
                    <div class="csv-upload-area">
                        <div class="upload-zone" id="csv-upload-zone">
                            <div class="upload-icon">📁</div>
                            <h3><?php _e('Drop CSV file here or click to browse', 'seo-page-generator-ai'); ?></h3>
                            <p><?php _e('CSV should have columns: keyword, location, custom_prompt (optional)', 'seo-page-generator-ai'); ?></p>
                            <input type="file" id="csv-file-input" name="csv_file" accept=".csv" style="display: none;">
                            <button type="button" class="button" onclick="document.getElementById('csv-file-input').click();">
                                <?php _e('Browse Files', 'seo-page-generator-ai'); ?>
                            </button>
                        </div>
                        
                        <div class="csv-template">
                            <h4><?php _e('CSV Template', 'seo-page-generator-ai'); ?></h4>
                            <p><?php _e('Download a template CSV file to get started:', 'seo-page-generator-ai'); ?></p>
                            <button type="button" class="button button-secondary" id="download-csv-template">
                                <?php _e('Download Template', 'seo-page-generator-ai'); ?>
                            </button>
                        </div>
                    </div>
                    
                    <div id="csv-preview" style="display: none;">
                        <h4><?php _e('CSV Preview', 'seo-page-generator-ai'); ?></h4>
                        <div id="csv-preview-content"></div>
                    </div>
                </div>
            </div>
            
            <!-- Step 2: Template Selection -->
            <div class="workflow-step-content" id="step-2" style="display: none;">
                <div class="step-header">
                    <h2><?php _e('Step 2: Choose Template', 'seo-page-generator-ai'); ?></h2>
                    <p><?php _e('Select a template that will be used to generate your content. Templates define the structure and sections of your pages.', 'seo-page-generator-ai'); ?></p>
                </div>
                
                <div class="template-selector">
                    <?php if (empty($templates)): ?>
                        <div class="no-templates">
                            <p><?php _e('No templates available. Please create a template first.', 'seo-page-generator-ai'); ?></p>
                            <a href="<?php echo admin_url('admin.php?page=spga-templates'); ?>" class="button button-primary">
                                <?php _e('Create Template', 'seo-page-generator-ai'); ?>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="template-grid">
                            <?php foreach ($templates as $template): ?>
                            <div class="template-option">
                                <input type="radio" id="template-<?php echo $template->id; ?>" name="template_id" value="<?php echo $template->id; ?>" <?php echo $template->is_default ? 'checked' : ''; ?>>
                                <label for="template-<?php echo $template->id; ?>" class="template-card">
                                    <div class="template-header">
                                        <h3><?php echo esc_html($template->name); ?></h3>
                                        <?php if ($template->is_default): ?>
                                            <span class="default-badge"><?php _e('Default', 'seo-page-generator-ai'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="template-description"><?php echo esc_html($template->description); ?></p>
                                    <div class="template-meta">
                                        <span class="post-type"><?php echo esc_html(ucfirst($template->post_type)); ?></span>
                                        <span class="usage-count"><?php echo sprintf(__('Used %d times', 'seo-page-generator-ai'), $template->usage_count); ?></span>
                                    </div>
                                </label>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="template-preview">
                            <h4><?php _e('Template Preview', 'seo-page-generator-ai'); ?></h4>
                            <div id="template-preview-content">
                                <p class="text-muted"><?php _e('Select a template to see its preview.', 'seo-page-generator-ai'); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Step 3: Generation Settings -->
            <div class="workflow-step-content" id="step-3" style="display: none;">
                <div class="step-header">
                    <h2><?php _e('Step 3: Generation Settings', 'seo-page-generator-ai'); ?></h2>
                    <p><?php _e('Configure how your content will be generated and published.', 'seo-page-generator-ai'); ?></p>
                </div>
                
                <div class="settings-grid">
                    <div class="setting-group">
                        <h4><?php _e('Content Type', 'seo-page-generator-ai'); ?></h4>
                        <label>
                            <input type="radio" name="post_type" value="post" checked>
                            <?php _e('Posts', 'seo-page-generator-ai'); ?>
                        </label>
                        <label>
                            <input type="radio" name="post_type" value="page">
                            <?php _e('Pages', 'seo-page-generator-ai'); ?>
                        </label>
                    </div>
                    
                    <div class="setting-group">
                        <h4><?php _e('Publishing Options', 'seo-page-generator-ai'); ?></h4>
                        <label>
                            <input type="radio" name="publish_status" value="draft" checked>
                            <?php _e('Save as Draft', 'seo-page-generator-ai'); ?>
                        </label>
                        <label>
                            <input type="radio" name="publish_status" value="publish">
                            <?php _e('Publish Immediately', 'seo-page-generator-ai'); ?>
                        </label>
                        <label>
                            <input type="radio" name="publish_status" value="schedule">
                            <?php _e('Schedule Publishing', 'seo-page-generator-ai'); ?>
                        </label>
                    </div>
                    
                    <div class="setting-group">
                        <h4><?php _e('SEO Options', 'seo-page-generator-ai'); ?></h4>
                        <label>
                            <input type="checkbox" name="enable_seo_optimization" value="1" checked>
                            <?php _e('Auto-assign focus keywords', 'seo-page-generator-ai'); ?>
                        </label>
                        <label>
                            <input type="checkbox" name="enable_internal_linking" value="1" checked>
                            <?php _e('Add internal links automatically', 'seo-page-generator-ai'); ?>
                        </label>
                        <label>
                            <input type="checkbox" name="generate_meta_descriptions" value="1" checked>
                            <?php _e('Generate meta descriptions', 'seo-page-generator-ai'); ?>
                        </label>
                    </div>
                    
                    <div class="setting-group">
                        <h4><?php _e('Processing Options', 'seo-page-generator-ai'); ?></h4>
                        <label for="batch-size">
                            <?php _e('Batch Size:', 'seo-page-generator-ai'); ?>
                            <input type="number" id="batch-size" name="batch_size" value="5" min="1" max="50">
                        </label>
                        <label for="processing-delay">
                            <?php _e('Delay between batches (seconds):', 'seo-page-generator-ai'); ?>
                            <input type="number" id="processing-delay" name="processing_delay" value="60" min="10" max="3600">
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Step 4: Review -->
            <div class="workflow-step-content" id="step-4" style="display: none;">
                <div class="step-header">
                    <h2><?php _e('Step 4: Review & Confirm', 'seo-page-generator-ai'); ?></h2>
                    <p><?php _e('Review your settings before starting the content generation process.', 'seo-page-generator-ai'); ?></p>
                </div>
                
                <div class="review-summary">
                    <div class="summary-section">
                        <h4><?php _e('Input Data', 'seo-page-generator-ai'); ?></h4>
                        <div id="review-input-data">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <h4><?php _e('Template', 'seo-page-generator-ai'); ?></h4>
                        <div id="review-template">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <h4><?php _e('Settings', 'seo-page-generator-ai'); ?></h4>
                        <div id="review-settings">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <h4><?php _e('Estimated Output', 'seo-page-generator-ai'); ?></h4>
                        <div id="review-output">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Step 5: Generate -->
            <div class="workflow-step-content" id="step-5" style="display: none;">
                <div class="step-header">
                    <h2><?php _e('Step 5: Content Generation', 'seo-page-generator-ai'); ?></h2>
                    <p><?php _e('Your content is being generated. You can monitor the progress below.', 'seo-page-generator-ai'); ?></p>
                </div>
                
                <div class="generation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="generation-progress-fill"></div>
                    </div>
                    <div class="progress-stats">
                        <span id="progress-text">0% Complete</span>
                        <span id="progress-items">0 of 0 items processed</span>
                    </div>
                </div>
                
                <div class="generation-log">
                    <h4><?php _e('Generation Log', 'seo-page-generator-ai'); ?></h4>
                    <div id="generation-log-content">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
                
                <div class="generation-actions" style="display: none;">
                    <button type="button" class="button button-secondary" id="pause-generation">
                        <?php _e('Pause', 'seo-page-generator-ai'); ?>
                    </button>
                    <button type="button" class="button button-secondary" id="stop-generation">
                        <?php _e('Stop', 'seo-page-generator-ai'); ?>
                    </button>
                </div>
            </div>
            
            <!-- Navigation Buttons -->
            <div class="workflow-navigation">
                <button type="button" class="button button-secondary" id="prev-step" style="display: none;">
                    <?php _e('Previous', 'seo-page-generator-ai'); ?>
                </button>
                <button type="button" class="button button-primary" id="next-step">
                    <?php _e('Next', 'seo-page-generator-ai'); ?>
                </button>
                <button type="submit" class="button button-primary" id="start-generation" style="display: none;">
                    <?php _e('Start Generation', 'seo-page-generator-ai'); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Workflow navigation logic will be added here
    let currentStep = 1;
    const totalSteps = 5;
    
    // Initialize workflow
    updateStepDisplay();
    
    // Step navigation
    $('#next-step').on('click', function() {
        if (validateCurrentStep()) {
            if (currentStep < totalSteps) {
                currentStep++;
                updateStepDisplay();
            }
        }
    });
    
    $('#prev-step').on('click', function() {
        if (currentStep > 1) {
            currentStep--;
            updateStepDisplay();
        }
    });
    
    function updateStepDisplay() {
        // Update progress indicator
        $('.progress-steps .step').removeClass('active completed');
        for (let i = 1; i <= totalSteps; i++) {
            if (i < currentStep) {
                $(`.step[data-step="${i}"]`).addClass('completed');
            } else if (i === currentStep) {
                $(`.step[data-step="${i}"]`).addClass('active');
            }
        }
        
        // Show/hide step content
        $('.workflow-step-content').hide();
        $(`#step-${currentStep}`).show();
        
        // Update navigation buttons
        $('#prev-step').toggle(currentStep > 1);
        $('#next-step').toggle(currentStep < totalSteps);
        $('#start-generation').toggle(currentStep === totalSteps);
        
        // Update review content if on step 4
        if (currentStep === 4) {
            updateReviewContent();
        }
    }
    
    function validateCurrentStep() {
        switch(currentStep) {
            case 1: // Input validation
                const inputMethod = $('input[name="input_method"]:checked').val();

                if (inputMethod === 'manual') {
                    let hasKeywords = false;
                    let hasLocations = false;

                    // Check bulk inputs first (if visible)
                    if ($('#keywords-bulk').is(':visible') && $('#keywords-input').val().trim()) {
                        hasKeywords = true;
                    } else {
                        // Check individual inputs
                        $('.keyword-input').each(function() {
                            if ($(this).val().trim() !== '') {
                                hasKeywords = true;
                            }
                        });
                    }

                    if ($('#locations-bulk').is(':visible') && $('#locations-input').val().trim()) {
                        hasLocations = true;
                    } else {
                        // Check individual inputs
                        $('.location-input').each(function() {
                            if ($(this).val().trim() !== '') {
                                hasLocations = true;
                            }
                        });
                    }

                    if (!hasKeywords || !hasLocations) {
                        alert('Please enter both keywords and locations.');
                        return false;
                    }
                } else if (inputMethod === 'csv') {
                    // Check if CSV file is uploaded
                    const csvFile = $('#csv-file').val();
                    if (!csvFile) {
                        alert('Please upload a CSV file.');
                        return false;
                    }
                }
                break;

            case 2: // Template selection
                const templateId = $('#template-select').val();
                if (!templateId) {
                    alert('Please select a template.');
                    return false;
                }
                break;

            case 3: // Generation settings
                // Optional validation for settings
                break;

            case 4: // Review step
                // No validation needed
                break;
        }

        return true;
    }
    
    function updateReviewContent() {
        // Populate review sections with current form data
        // This will be implemented with the actual form data
    }
    
    // Input method toggle
    $('input[name="input_method"]').on('change', function() {
        if ($(this).val() === 'manual') {
            $('#manual-input-section').show();
            $('#csv-input-section').hide();
        } else {
            $('#manual-input-section').hide();
            $('#csv-input-section').show();
        }
    });

    // Keywords bulk/individual toggle
    $('#toggle-keywords-bulk').on('click', function() {
        if ($('#keywords-bulk').is(':visible')) {
            // Switch to individual
            $('#keywords-bulk').hide();
            $('#keywords-individual').show();
            $(this).text('<?php _e('Bulk Input', 'seo-page-generator-ai'); ?>');
        } else {
            // Switch to bulk
            $('#keywords-individual').hide();
            $('#keywords-bulk').show();
            $(this).text('<?php _e('Individual Input', 'seo-page-generator-ai'); ?>');
        }
        updateCombinationPreview();
    });

    // Locations bulk/individual toggle
    $('#toggle-locations-bulk').on('click', function() {
        if ($('#locations-bulk').is(':visible')) {
            // Switch to individual
            $('#locations-bulk').hide();
            $('#locations-individual').show();
            $(this).text('<?php _e('Bulk Input', 'seo-page-generator-ai'); ?>');
        } else {
            // Switch to bulk
            $('#locations-individual').hide();
            $('#locations-bulk').show();
            $(this).text('<?php _e('Individual Input', 'seo-page-generator-ai'); ?>');
        }
        updateCombinationPreview();
    });
    
    // Real-time combination preview
    $('#keywords-input, #locations-input').on('input', function() {
        updateCombinationPreview();
    });

    // Also update preview when individual inputs change
    $(document).on('input', '.keyword-input, .location-input', function() {
        updateCombinationPreview();
    });

    function updateCombinationPreview() {
        // Collect keywords from both individual and bulk inputs
        let keywords = [];
        if ($('#keywords-bulk').is(':visible') && $('#keywords-input').val().trim()) {
            keywords = $('#keywords-input').val().split('\n').filter(k => k.trim());
        } else {
            $('.keyword-input').each(function() {
                const value = $(this).val().trim();
                if (value) keywords.push(value);
            });
        }

        // Collect locations from both individual and bulk inputs
        let locations = [];
        if ($('#locations-bulk').is(':visible') && $('#locations-input').val().trim()) {
            locations = $('#locations-input').val().split('\n').filter(l => l.trim());
        } else {
            $('.location-input').each(function() {
                const value = $(this).val().trim();
                if (value) locations.push(value);
            });
        }

        if (keywords.length && locations.length) {
            const combinations = [];
            keywords.forEach(keyword => {
                locations.forEach(location => {
                    combinations.push(`${keyword.trim()} ${location.trim()}`);
                });
            });

            const preview = combinations.slice(0, 10).map(combo =>
                `<span class="combination-item">${combo}</span>`
            ).join('');

            const total = combinations.length;
            const showing = Math.min(10, total);

            $('#combinations-list').html(`
                <div class="combinations-preview">${preview}</div>
                <p class="combinations-count">
                    Showing ${showing} of ${total} combinations
                    ${total > 10 ? `<span class="text-muted">(${total - 10} more...)</span>` : ''}
                </p>
            `);
        } else {
            $('#combinations-list').html('<p class="text-muted">Enter keywords and locations above to see the combinations that will be generated.</p>');
        }
    }

    // Start Generation button handler
    $('#start-generation').on('click', function(e) {
        e.preventDefault();

        // Disable button and show loading state
        $(this).prop('disabled', true).text('Processing...').addClass('spga-loading');

        // Collect workflow data
        const workflowData = {
            input_method: $('input[name="input_method"]:checked').val(),
            keywords: collectKeywords(),
            locations: collectLocations(),
            template_id: $('#template-select').val(),
            post_type: $('#post-type-select').val(),
            post_status: $('#post-status-select').val(),
            generate_meta_title: $('#generate-meta-title').is(':checked'),
            generate_meta_description: $('#generate-meta-description').is(':checked'),
            batch_size: $('#batch-size').val(),
            processing_delay: $('#processing-delay').val()
        };

        // Show progress section
        $('.generation-progress').show();
        $('#generation-progress-fill').css('width', '0%');
        $('#progress-text').text('0% Complete');
        $('#progress-items').text('0 of 0 items processed');
        $('#generation-log-content').html('<p>🚀 Starting content generation...</p>');

        // Submit workflow via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'spga_submit_workflow',
                nonce: '<?php echo wp_create_nonce('spga_nonce'); ?>',
                workflow_data: workflowData
            },
            success: function(response) {
                if (response.success) {
                    $('#generation-log-content').append(`<p>✅ ${response.data.message}</p>`);

                    // Show redirect option after 2 seconds
                    setTimeout(function() {
                        const redirectHtml = `
                            <div style="margin-top: 20px; padding: 15px; background: #f0f6ff; border: 1px solid #c3dafe; border-radius: 6px;">
                                <p><strong>🎯 Track Your Progress</strong></p>
                                <p>Your content generation has started! You can monitor the progress in real-time.</p>
                                <a href="<?php echo admin_url('admin.php?page=spga-queue'); ?>" class="button button-primary">
                                    📊 Go to Queue Management
                                </a>
                            </div>
                        `;
                        $('#generation-log-content').append(redirectHtml);
                    }, 2000);
                } else {
                    $('#generation-log-content').append(`<p>❌ Error: ${response.data}</p>`);
                }
            },
            error: function(xhr, status, error) {
                $('#generation-log-content').append(`<p>❌ AJAX Error: ${error}</p>`);
            },
            complete: function() {
                // Re-enable button
                $('#start-generation').prop('disabled', false).text('Start Generation').removeClass('spga-loading');
            }
        });
    });

    // Helper function to collect keywords from both individual and bulk inputs
    function collectKeywords() {
        let keywords = [];

        // Check if bulk input is visible and has content
        if ($('#keywords-bulk').is(':visible') && $('#keywords-input').val().trim()) {
            keywords = $('#keywords-input').val().split('\n').filter(k => k.trim());
        } else {
            // Collect from individual inputs
            $('.keyword-input').each(function() {
                const value = $(this).val().trim();
                if (value) {
                    keywords.push(value);
                }
            });
        }

        return keywords.join('\n');
    }

    // Helper function to collect locations from both individual and bulk inputs
    function collectLocations() {
        let locations = [];

        // Check if bulk input is visible and has content
        if ($('#locations-bulk').is(':visible') && $('#locations-input').val().trim()) {
            locations = $('#locations-input').val().split('\n').filter(l => l.trim());
        } else {
            // Collect from individual inputs
            $('.location-input').each(function() {
                const value = $(this).val().trim();
                if (value) {
                    locations.push(value);
                }
            });
        }

        return locations.join('\n');
    }
});
</script>
    </div>
</div>
