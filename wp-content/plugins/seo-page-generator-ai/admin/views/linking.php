<?php
/**
 * Internal Linking Management view for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle form submissions
if (isset($_POST['action'])) {
    check_admin_referer('spga_linking_action', 'spga_linking_nonce');
    
    $action = sanitize_text_field($_POST['action']);
    
    switch ($action) {
        case 'add_linking_rule':
            $rule_data = array(
                'keyword' => sanitize_text_field($_POST['keyword']),
                'target_url' => esc_url_raw($_POST['target_url']),
                'target_post_id' => intval($_POST['target_post_id']),
                'link_text' => sanitize_text_field($_POST['link_text']),
                'max_links_per_page' => intval($_POST['max_links_per_page']),
                'is_active' => intval($_POST['is_active'] ?? 1),
                'priority' => intval($_POST['priority'])
            );
            
            $result = spga()->internal_linker->create_linking_rule($rule_data);
            
            if ($result) {
                echo '<div class="notice notice-success"><p>' . __('Linking rule added successfully!', 'seo-page-generator-ai') . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . __('Failed to add linking rule.', 'seo-page-generator-ai') . '</p></div>';
            }
            break;
            
        case 'update_linking_rule':
            $rule_id = intval($_POST['rule_id']);
            $rule_data = array(
                'keyword' => sanitize_text_field($_POST['keyword']),
                'target_url' => esc_url_raw($_POST['target_url']),
                'target_post_id' => intval($_POST['target_post_id']),
                'link_text' => sanitize_text_field($_POST['link_text']),
                'max_links_per_page' => intval($_POST['max_links_per_page']),
                'is_active' => intval($_POST['is_active'] ?? 1),
                'priority' => intval($_POST['priority'])
            );
            
            $result = spga()->internal_linker->update_linking_rule($rule_id, $rule_data);
            
            if ($result !== false) {
                echo '<div class="notice notice-success"><p>' . __('Linking rule updated successfully!', 'seo-page-generator-ai') . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . __('Failed to update linking rule.', 'seo-page-generator-ai') . '</p></div>';
            }
            break;
            
        case 'delete_linking_rules':
            if (isset($_POST['rule_ids']) && is_array($_POST['rule_ids'])) {
                $deleted = 0;
                foreach ($_POST['rule_ids'] as $rule_id) {
                    if (spga()->internal_linker->delete_linking_rule(intval($rule_id))) {
                        $deleted++;
                    }
                }
                echo '<div class="notice notice-success"><p>' . sprintf(__('Deleted %d linking rules.', 'seo-page-generator-ai'), $deleted) . '</p></div>';
            }
            break;
            
        case 'import_default_rules':
            spga()->internal_linker->import_default_rules();
            echo '<div class="notice notice-success"><p>' . __('Default linking rules imported successfully!', 'seo-page-generator-ai') . '</p></div>';
            break;
            
        case 'bulk_process_links':
            $post_types = array_map('sanitize_text_field', $_POST['post_types'] ?? array('post', 'page'));
            $limit = intval($_POST['process_limit'] ?? 50);
            
            // Get posts to process
            $posts = get_posts(array(
                'post_type' => $post_types,
                'post_status' => 'publish',
                'numberposts' => $limit,
                'meta_query' => array(
                    array(
                        'key' => '_spga_links_processed',
                        'compare' => 'NOT EXISTS'
                    )
                )
            ));
            
            $processed = 0;
            foreach ($posts as $post) {
                $links_added = spga()->internal_linker->process_post_links($post->ID);
                if ($links_added !== false) {
                    update_post_meta($post->ID, '_spga_links_processed', 1);
                    $processed++;
                }
            }
            
            echo '<div class="notice notice-success"><p>' . sprintf(__('Processed internal links for %d posts.', 'seo-page-generator-ai'), $processed) . '</p></div>';
            break;
    }
}

// Get current action
$current_action = sanitize_text_field($_GET['action'] ?? 'list');
$rule_id = intval($_GET['rule_id'] ?? 0);

// Get linking rules
$linking_rules = spga()->internal_linker->get_all_linking_rules();

// Get linking statistics
$linking_stats = spga()->internal_linker->get_linking_statistics();

// Get posts for target selection
$posts = get_posts(array(
    'post_type' => array('post', 'page'),
    'post_status' => 'publish',
    'numberposts' => 100,
    'orderby' => 'title',
    'order' => 'ASC'
));
?>

<div class="wrap spga-linking">
    <h1 class="wp-heading-inline"><?php _e('Internal Linking', 'seo-page-generator-ai'); ?></h1>
    
    <?php if ($current_action === 'list'): ?>
        <a href="<?php echo admin_url('admin.php?page=spga-linking&action=new'); ?>" class="page-title-action">
            <?php _e('Add New Rule', 'seo-page-generator-ai'); ?>
        </a>
    <?php endif; ?>
    
    <hr class="wp-header-end">
    
    <?php if ($current_action === 'list'): ?>
        <!-- Statistics Cards -->
        <div class="spga-stats-grid">
            <div class="spga-stat-card">
                <div class="stat-icon">🔗</div>
                <div class="stat-content">
                    <h3><?php echo number_format($linking_stats->total_posts ?? 0); ?></h3>
                    <p><?php _e('Posts with Links', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
            
            <div class="spga-stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <h3><?php echo number_format($linking_stats->total_links_added ?? 0); ?></h3>
                    <p><?php _e('Total Links Added', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
            
            <div class="spga-stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-content">
                    <h3><?php echo number_format($linking_stats->avg_links_per_post ?? 0, 1); ?></h3>
                    <p><?php _e('Avg Links per Post', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
            
            <div class="spga-stat-card">
                <div class="stat-icon">⚙️</div>
                <div class="stat-content">
                    <h3><?php echo count($linking_rules); ?></h3>
                    <p><?php _e('Active Rules', 'seo-page-generator-ai'); ?></p>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="spga-quick-actions">
            <h2><?php _e('Quick Actions', 'seo-page-generator-ai'); ?></h2>
            
            <div class="action-buttons">
                <form method="post" style="display: inline;">
                    <?php wp_nonce_field('spga_linking_action', 'spga_linking_nonce'); ?>
                    <input type="hidden" name="action" value="import_default_rules">
                    <input type="submit" class="button button-secondary" value="<?php esc_attr_e('Import Default Rules', 'seo-page-generator-ai'); ?>" onclick="return confirm('<?php esc_attr_e('This will add default debt-related keywords. Continue?', 'seo-page-generator-ai'); ?>');">
                </form>
                
                <button type="button" class="button button-primary" id="bulk-process-links">
                    <?php _e('Bulk Process Links', 'seo-page-generator-ai'); ?>
                </button>
            </div>
        </div>
        
        <!-- Linking Rules List -->
        <div class="spga-linking-rules">
            <h2><?php _e('Linking Rules', 'seo-page-generator-ai'); ?></h2>
            
            <?php if (empty($linking_rules)): ?>
                <div class="no-rules">
                    <h3><?php _e('No Linking Rules Found', 'seo-page-generator-ai'); ?></h3>
                    <p><?php _e('Create linking rules to automatically add internal links to your content.', 'seo-page-generator-ai'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=spga-linking&action=new'); ?>" class="button button-primary">
                        <?php _e('Create First Rule', 'seo-page-generator-ai'); ?>
                    </a>
                </div>
            <?php else: ?>
                <form method="post">
                    <?php wp_nonce_field('spga_linking_action', 'spga_linking_nonce'); ?>
                    <input type="hidden" name="action" value="delete_linking_rules">
                    
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <td class="manage-column column-cb check-column">
                                    <input type="checkbox" id="cb-select-all-rules">
                                </td>
                                <th><?php _e('Keyword', 'seo-page-generator-ai'); ?></th>
                                <th><?php _e('Target', 'seo-page-generator-ai'); ?></th>
                                <th><?php _e('Link Text', 'seo-page-generator-ai'); ?></th>
                                <th><?php _e('Max Links', 'seo-page-generator-ai'); ?></th>
                                <th><?php _e('Priority', 'seo-page-generator-ai'); ?></th>
                                <th><?php _e('Status', 'seo-page-generator-ai'); ?></th>
                                <th><?php _e('Actions', 'seo-page-generator-ai'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($linking_rules as $rule): ?>
                            <tr>
                                <th scope="row" class="check-column">
                                    <input type="checkbox" name="rule_ids[]" value="<?php echo $rule->id; ?>">
                                </th>
                                <td><strong><?php echo esc_html($rule->keyword); ?></strong></td>
                                <td>
                                    <?php if (!empty($rule->target_url)): ?>
                                        <a href="<?php echo esc_url($rule->target_url); ?>" target="_blank">
                                            <?php echo esc_html(wp_trim_words($rule->target_url, 5)); ?>
                                        </a>
                                    <?php elseif (!empty($rule->target_post_id)): ?>
                                        <?php 
                                        $target_post = get_post($rule->target_post_id);
                                        if ($target_post): ?>
                                            <a href="<?php echo get_edit_post_link($rule->target_post_id); ?>" target="_blank">
                                                <?php echo esc_html($target_post->post_title); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php _e('Post not found', 'seo-page-generator-ai'); ?></span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php _e('Auto-detect', 'seo-page-generator-ai'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($rule->link_text ?: $rule->keyword); ?></td>
                                <td><?php echo $rule->max_links_per_page; ?></td>
                                <td>
                                    <span class="priority-badge priority-<?php echo $rule->priority; ?>">
                                        <?php echo $rule->priority; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $rule->is_active ? 'active' : 'inactive'; ?>">
                                        <?php echo $rule->is_active ? __('Active', 'seo-page-generator-ai') : __('Inactive', 'seo-page-generator-ai'); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="row-actions">
                                        <span class="edit">
                                            <a href="<?php echo admin_url('admin.php?page=spga-linking&action=edit&rule_id=' . $rule->id); ?>">
                                                <?php _e('Edit', 'seo-page-generator-ai'); ?>
                                            </a> |
                                        </span>
                                        <span class="delete">
                                            <a href="#" class="delete-rule" data-rule-id="<?php echo $rule->id; ?>">
                                                <?php _e('Delete', 'seo-page-generator-ai'); ?>
                                            </a>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    
                    <div class="tablenav bottom">
                        <div class="alignleft actions bulkactions">
                            <select name="bulk_action">
                                <option value="-1"><?php _e('Bulk Actions', 'seo-page-generator-ai'); ?></option>
                                <option value="delete"><?php _e('Delete', 'seo-page-generator-ai'); ?></option>
                            </select>
                            <input type="submit" class="button action" value="<?php esc_attr_e('Apply', 'seo-page-generator-ai'); ?>" onclick="return confirm('<?php esc_attr_e('Are you sure you want to delete the selected rules?', 'seo-page-generator-ai'); ?>');">
                        </div>
                    </div>
                </form>
            <?php endif; ?>
        </div>
        
    <?php elseif ($current_action === 'new' || $current_action === 'edit'): ?>
        <!-- Rule Editor -->
        <?php
        $rule = null;
        if ($current_action === 'edit' && $rule_id) {
            global $wpdb;
            $linking_table = $wpdb->prefix . 'spga_linking_rules';
            $rule = $wpdb->get_row($wpdb->prepare("SELECT * FROM $linking_table WHERE id = %d", $rule_id));
        }
        ?>
        
        <div class="rule-editor">
            <form method="post">
                <?php wp_nonce_field('spga_linking_action', 'spga_linking_nonce'); ?>
                <input type="hidden" name="action" value="<?php echo $current_action === 'edit' ? 'update_linking_rule' : 'add_linking_rule'; ?>">
                <?php if ($rule): ?>
                    <input type="hidden" name="rule_id" value="<?php echo $rule->id; ?>">
                <?php endif; ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="keyword"><?php _e('Keyword', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="keyword" name="keyword" value="<?php echo esc_attr($rule->keyword ?? ''); ?>" class="regular-text" required>
                            <p class="description"><?php _e('The keyword or phrase to search for in content.', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="target_type"><?php _e('Link Target', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <div class="target-type-selector">
                                <label>
                                    <input type="radio" name="target_type" value="auto" <?php checked(empty($rule->target_url) && empty($rule->target_post_id)); ?>>
                                    <?php _e('Auto-detect relevant posts', 'seo-page-generator-ai'); ?>
                                </label>
                                <label>
                                    <input type="radio" name="target_type" value="post" <?php checked(!empty($rule->target_post_id)); ?>>
                                    <?php _e('Specific post/page', 'seo-page-generator-ai'); ?>
                                </label>
                                <label>
                                    <input type="radio" name="target_type" value="url" <?php checked(!empty($rule->target_url)); ?>>
                                    <?php _e('Custom URL', 'seo-page-generator-ai'); ?>
                                </label>
                            </div>
                            
                            <div class="target-options">
                                <div class="target-option" id="target-post-option">
                                    <select name="target_post_id" class="regular-text">
                                        <option value=""><?php _e('Select a post/page...', 'seo-page-generator-ai'); ?></option>
                                        <?php foreach ($posts as $post): ?>
                                            <option value="<?php echo $post->ID; ?>" <?php selected($rule->target_post_id ?? 0, $post->ID); ?>>
                                                <?php echo esc_html($post->post_title . ' (' . ucfirst($post->post_type) . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="target-option" id="target-url-option">
                                    <input type="url" name="target_url" value="<?php echo esc_attr($rule->target_url ?? ''); ?>" class="regular-text" placeholder="https://example.com/page">
                                </div>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="link_text"><?php _e('Link Text', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="link_text" name="link_text" value="<?php echo esc_attr($rule->link_text ?? ''); ?>" class="regular-text">
                            <p class="description"><?php _e('Custom link text. Leave empty to use the matched keyword.', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="max_links_per_page"><?php _e('Max Links per Page', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="max_links_per_page" name="max_links_per_page" value="<?php echo esc_attr($rule->max_links_per_page ?? 3); ?>" min="1" max="10">
                            <p class="description"><?php _e('Maximum number of times this keyword should be linked per page.', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="priority"><?php _e('Priority', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="priority" name="priority" value="<?php echo esc_attr($rule->priority ?? 1); ?>" min="0" max="10">
                            <p class="description"><?php _e('Higher priority rules are processed first (0-10).', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="is_active"><?php _e('Status', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="checkbox" id="is_active" name="is_active" value="1" <?php checked($rule->is_active ?? 1, 1); ?>>
                            <label for="is_active"><?php _e('Active', 'seo-page-generator-ai'); ?></label>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" class="button button-primary" value="<?php echo $current_action === 'edit' ? __('Update Rule', 'seo-page-generator-ai') : __('Add Rule', 'seo-page-generator-ai'); ?>">
                    <a href="<?php echo admin_url('admin.php?page=spga-linking'); ?>" class="button">
                        <?php _e('Cancel', 'seo-page-generator-ai'); ?>
                    </a>
                </p>
            </form>
        </div>
        
    <?php endif; ?>
</div>

<!-- Bulk Process Modal -->
<div id="bulk-process-modal" class="spga-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><?php _e('Bulk Process Internal Links', 'seo-page-generator-ai'); ?></h3>
            <button type="button" class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form method="post">
                <?php wp_nonce_field('spga_linking_action', 'spga_linking_nonce'); ?>
                <input type="hidden" name="action" value="bulk_process_links">
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Post Types', 'seo-page-generator-ai'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="post_types[]" value="post" checked>
                                <?php _e('Posts', 'seo-page-generator-ai'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="post_types[]" value="page" checked>
                                <?php _e('Pages', 'seo-page-generator-ai'); ?>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="process_limit"><?php _e('Limit', 'seo-page-generator-ai'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="process_limit" name="process_limit" value="50" min="1" max="500">
                            <p class="description"><?php _e('Number of posts to process in this batch.', 'seo-page-generator-ai'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" class="button button-primary" value="<?php esc_attr_e('Start Processing', 'seo-page-generator-ai'); ?>">
                    <button type="button" class="button modal-close"><?php _e('Cancel', 'seo-page-generator-ai'); ?></button>
                </p>
            </form>
        </div>
    </div>
</div>

<style>
.target-type-selector label {
    display: block;
    margin-bottom: 10px;
}

.target-options {
    margin-top: 15px;
}

.target-option {
    display: none;
    margin-bottom: 10px;
}

.target-option.active {
    display: block;
}

.no-rules {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Target type selector
    $('input[name="target_type"]').on('change', function() {
        var type = $(this).val();
        
        $('.target-option').removeClass('active');
        
        if (type === 'post') {
            $('#target-post-option').addClass('active');
        } else if (type === 'url') {
            $('#target-url-option').addClass('active');
        }
    });
    
    // Initialize target type on page load
    $('input[name="target_type"]:checked').trigger('change');
    
    // Bulk process modal
    $('#bulk-process-links').on('click', function() {
        $('#bulk-process-modal').show();
    });
    
    // Modal close
    $('.modal-close, .spga-modal').on('click', function(e) {
        if (e.target === this) {
            $('.spga-modal').hide();
        }
    });
    
    // Delete rule
    $('.delete-rule').on('click', function(e) {
        e.preventDefault();
        if (confirm('<?php _e('Are you sure you want to delete this linking rule?', 'seo-page-generator-ai'); ?>')) {
            var ruleId = $(this).data('rule-id');
            var form = $('<form method="post">')
                .append('<?php wp_nonce_field('spga_linking_action', 'spga_linking_nonce', true, false); ?>')
                .append('<input type="hidden" name="action" value="delete_linking_rules">')
                .append('<input type="hidden" name="rule_ids[]" value="' + ruleId + '">');
            
            $('body').append(form);
            form.submit();
        }
    });
    
    // Select all checkbox
    $('#cb-select-all-rules').on('change', function() {
        $('input[name="rule_ids[]"]').prop('checked', $(this).prop('checked'));
    });
});
</script>
