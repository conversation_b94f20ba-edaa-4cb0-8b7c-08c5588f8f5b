# SEO Page Generator AI - Processing Setup Guide

## 🚨 **CRITICAL SETUP REQUIREMENTS**

### **1. API Configuration (REQUIRED)**
**❌ Without this, processing will fail silently**

1. **Go to:** `SEO Generator AI > Settings`
2. **Configure API Provider:**
   - **Gemini API (Recommended)**: Get free API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - **OpenAI API (Fallback)**: Get API key from [OpenAI Platform](https://platform.openai.com/api-keys)
3. **Enter API Key** and click "Save Settings"
4. **Test API** using the "Test API" button

### **2. Database Tables (REQUIRED)**
**❌ Without these, queue items cannot be stored**

1. **Go to:** `SEO Generator AI > Diagnostics`
2. **Check Database Status** - all tables should show "✅ Exists"
3. **If missing:** Click "Create Tables" button

### **3. WordPress Cron (REQUIRED)**
**❌ Without this, automatic processing won't work**

1. **Go to:** `SEO Generator AI > Diagnostics`
2. **Check Cron Status:**
   - WP Cron should be "✅ Enabled"
   - Next Scheduled should show a future date/time
3. **If issues:** Click "Schedule Now" button

### **4. Queue Status (REQUIRED)**
**❌ If paused or locked, processing won't start**

1. **Go to:** `SEO Generator AI > Diagnostics`
2. **Check Queue Status:**
   - Queue Status should be "▶️ Active" (not paused)
   - Processing Lock should be "🔓 Unlocked"
3. **If issues:** Use "Unpause" or "Clear Lock" buttons

---

## 🔧 **STEP-BY-STEP TROUBLESHOOTING**

### **Step 1: Complete the Workflow**
1. **Go to:** `SEO Generator AI > Workflow`
2. **Enter Keywords:** e.g., "gaming", "programming"
3. **Enter Locations:** e.g., "London", "UK"
4. **Select Template:** Choose any template
5. **Click "Start Generation"**
6. **Should see:** "Added X items to the queue" message

### **Step 2: Check Queue Items**
1. **Go to:** `SEO Generator AI > Queue Management`
2. **Should see:** Your items with status "Pending"
3. **If no items:** Workflow submission failed - check browser console for errors

### **Step 3: Trigger Processing**
1. **In Queue Management:** Click "Start Processing" button
2. **For Local Development:** Use "Manual Process" button (if visible)
3. **Should see:** Status change from "Pending" to "Processing" to "Completed"

### **Step 4: Check for Errors**
1. **Go to:** `SEO Generator AI > Diagnostics`
2. **Check Recent Logs** for error messages
3. **Common errors:**
   - "API key not configured"
   - "Invalid API response"
   - "Database table missing"

---

## 🐛 **COMMON ISSUES & SOLUTIONS**

### **Issue: "Processing" but nothing happens**
**Causes:**
- API key missing or invalid
- WordPress cron disabled
- Processing lock stuck

**Solutions:**
1. Configure API key in Settings
2. Enable WordPress cron (remove `DISABLE_WP_CRON` from wp-config.php)
3. Clear processing lock in Diagnostics

### **Issue: "Please enter both keywords and locations"**
**Causes:**
- JavaScript validation error
- Input fields not properly detected

**Solutions:**
1. Refresh the page
2. Use bulk input mode (default)
3. Check browser console for JavaScript errors

### **Issue: Items stuck in "Pending" status**
**Causes:**
- Cron not running
- Queue paused
- Processing lock active

**Solutions:**
1. Use "Manual Process" button in Queue Management
2. Check Diagnostics page for cron/queue issues
3. Unpause queue if needed

### **Issue: Items fail with errors**
**Causes:**
- Invalid API key
- API rate limits
- Template errors

**Solutions:**
1. Test API in Settings page
2. Reduce batch size in Settings
3. Check template syntax

---

## ⚙️ **RECOMMENDED SETTINGS**

### **For Local Development:**
- **Batch Size:** 1-2 items
- **Processing Delay:** 10-30 seconds
- **Use Manual Processing:** Enable WP_DEBUG to see manual process button

### **For Production:**
- **Batch Size:** 5-10 items
- **Processing Delay:** 60-120 seconds
- **Auto Publish:** Disable (generate as drafts first)

### **API Usage Optimization:**
- **Gemini API:** Very cost-effective (~$0.001 per 1000 tokens)
- **OpenAI API:** More expensive but reliable fallback
- **Word Count:** Keep reasonable (500-1500 words per article)

---

## 🔍 **DEBUGGING CHECKLIST**

**Before asking for help, verify:**

- [ ] ✅ API key configured and tested
- [ ] ✅ Database tables exist
- [ ] ✅ WordPress cron enabled and scheduled
- [ ] ✅ Queue not paused
- [ ] ✅ No processing locks
- [ ] ✅ Items added to queue successfully
- [ ] ✅ No JavaScript errors in browser console
- [ ] ✅ WordPress debug log checked for PHP errors

**Enable Debug Mode:**
```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

**Check Debug Log:**
- Location: `/wp-content/debug.log`
- Look for: "SPGA" or "SEO Page Generator" errors

---

## 📞 **Getting Help**

If processing still doesn't work after following this guide:

1. **Go to Diagnostics page** and screenshot the status
2. **Check browser console** for JavaScript errors
3. **Check WordPress debug log** for PHP errors
4. **Note your setup:** Local development vs. production, hosting provider, etc.
5. **Provide specific error messages** from logs or diagnostics

**Most issues are resolved by:**
1. Configuring API keys properly
2. Ensuring WordPress cron is working
3. Using manual processing for testing
