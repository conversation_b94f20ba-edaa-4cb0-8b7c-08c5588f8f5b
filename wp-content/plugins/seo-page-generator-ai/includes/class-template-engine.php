<?php
/**
 * Template Engine for SEO Page Generator AI
 * Handles template processing and shortcode replacement
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_Template_Engine {
    
    /**
     * Available template variables
     */
    private $template_variables = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_shortcodes'));
        $this->init_template_variables();
    }
    
    /**
     * Initialize available template variables
     */
    private function init_template_variables() {
        $this->template_variables = array(
            'keyword' => __('Primary keyword', 'seo-page-generator-ai'),
            'location' => __('Target location', 'seo-page-generator-ai'),
            'title' => __('Generated page title', 'seo-page-generator-ai'),
            'meta_title' => __('SEO meta title', 'seo-page-generator-ai'),
            'meta_description' => __('SEO meta description', 'seo-page-generator-ai'),
            'focus_keyword' => __('Focus keyword for SEO', 'seo-page-generator-ai'),
            'current_date' => __('Current date', 'seo-page-generator-ai'),
            'current_year' => __('Current year', 'seo-page-generator-ai'),
            'site_name' => __('Website name', 'seo-page-generator-ai'),
            'site_url' => __('Website URL', 'seo-page-generator-ai')
        );
    }
    
    /**
     * Register shortcodes for template processing
     */
    public function register_shortcodes() {
        add_shortcode('spga_dynamic', array($this, 'process_dynamic_shortcode'));
        add_shortcode('spga_variable', array($this, 'process_variable_shortcode'));
        add_shortcode('spga_content', array($this, 'process_content_shortcode'));
        
        // Legacy support for Dynamic SEO shortcodes
        add_shortcode('dsc_dynamic', array($this, 'process_legacy_shortcode'));
    }
    
    /**
     * Process dynamic content shortcode
     * [spga_dynamic name="section_name"]
     */
    public function process_dynamic_shortcode($atts, $content = '') {
        $atts = shortcode_atts(array(
            'name' => '',
            'default' => '',
            'word_count' => 0
        ), $atts);
        
        if (empty($atts['name'])) {
            return $atts['default'];
        }
        
        // Get generated content for this section
        $generated_content = $this->get_generated_content($atts['name']);
        
        if (!empty($generated_content)) {
            return $generated_content;
        }
        
        return $atts['default'];
    }
    
    /**
     * Process template variable shortcode
     * [spga_variable name="keyword"]
     */
    public function process_variable_shortcode($atts, $content = '') {
        $atts = shortcode_atts(array(
            'name' => '',
            'default' => '',
            'format' => ''
        ), $atts);
        
        if (empty($atts['name'])) {
            return $atts['default'];
        }
        
        $value = $this->get_template_variable($atts['name']);
        
        if (!empty($value) && !empty($atts['format'])) {
            $value = $this->format_variable($value, $atts['format']);
        }
        
        return !empty($value) ? $value : $atts['default'];
    }
    
    /**
     * Process content shortcode for AI-generated sections
     * [spga_content section="intro" prompt="Generate introduction"]
     */
    public function process_content_shortcode($atts, $content = '') {
        $atts = shortcode_atts(array(
            'section' => '',
            'prompt' => '',
            'word_count' => 100,
            'default' => ''
        ), $atts);
        
        if (empty($atts['section'])) {
            return $atts['default'];
        }
        
        // This will be populated during content generation
        $generated_content = $this->get_generated_content($atts['section']);
        
        return !empty($generated_content) ? $generated_content : $atts['default'];
    }
    
    /**
     * Process legacy shortcode for backward compatibility
     * [dsc_dynamic name="section_name"]
     */
    public function process_legacy_shortcode($atts, $content = '') {
        return $this->process_dynamic_shortcode($atts, $content);
    }
    
    /**
     * Process template with given data
     */
    public function process_template($template_content, $data = array()) {
        // Set template variables
        $this->set_template_variables($data);
        
        // Replace basic variables first
        $processed_content = $this->replace_template_variables($template_content, $data);
        
        // Process shortcodes
        $processed_content = do_shortcode($processed_content);
        
        return $processed_content;
    }
    
    /**
     * Replace template variables in content
     */
    private function replace_template_variables($content, $data = array()) {
        $replacements = array();
        
        // Basic variable replacements
        foreach ($data as $key => $value) {
            $replacements["[{$key}]"] = $value;
            $replacements["{{$key}}"] = $value;
            $replacements["{{{$key}}}"] = $value;
        }
        
        // System variables
        $replacements['[current_date]'] = date_i18n(get_option('date_format'));
        $replacements['[current_year]'] = date('Y');
        $replacements['[site_name]'] = get_bloginfo('name');
        $replacements['[site_url]'] = get_site_url();
        
        // Legacy replacements
        $replacements['[TITLEOFPAGE]'] = isset($data['title']) ? $data['title'] : '';
        
        return str_replace(array_keys($replacements), array_values($replacements), $content);
    }
    
    /**
     * Set template variables for current processing
     */
    public function set_template_variables($variables) {
        $this->current_variables = $variables;
    }
    
    /**
     * Get template variable value
     */
    private function get_template_variable($name) {
        if (isset($this->current_variables[$name])) {
            return $this->current_variables[$name];
        }
        
        // System variables
        switch ($name) {
            case 'current_date':
                return date_i18n(get_option('date_format'));
            case 'current_year':
                return date('Y');
            case 'site_name':
                return get_bloginfo('name');
            case 'site_url':
                return get_site_url();
            default:
                return '';
        }
    }
    
    /**
     * Format variable value
     */
    private function format_variable($value, $format) {
        switch ($format) {
            case 'uppercase':
                return strtoupper($value);
            case 'lowercase':
                return strtolower($value);
            case 'title':
                return ucwords($value);
            case 'sentence':
                return ucfirst(strtolower($value));
            default:
                return $value;
        }
    }
    
    /**
     * Get generated content for a section
     */
    private function get_generated_content($section_name) {
        // This will be populated during AI content generation
        if (isset($this->generated_sections[$section_name])) {
            return $this->generated_sections[$section_name];
        }
        
        return '';
    }
    
    /**
     * Set generated content for sections
     */
    public function set_generated_content($sections) {
        $this->generated_sections = $sections;
    }
    
    /**
     * Get template by ID
     */
    public function get_template($template_id) {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'spga_templates';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $templates_table WHERE id = %d AND is_active = 1",
            $template_id
        ));
    }
    
    /**
     * Get all active templates
     */
    public function get_templates() {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'spga_templates';
        
        return $wpdb->get_results(
            "SELECT * FROM $templates_table WHERE is_active = 1 ORDER BY is_default DESC, name ASC"
        );
    }
    
    /**
     * Create new template
     */
    public function create_template($data) {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'spga_templates';
        
        $template_data = array(
            'name' => sanitize_text_field($data['name']),
            'description' => sanitize_textarea_field($data['description']),
            'content' => wp_kses_post($data['content']),
            'variables' => json_encode($data['variables']),
            'post_type' => sanitize_text_field($data['post_type']),
            'category_id' => intval($data['category_id']),
            'tags' => sanitize_text_field($data['tags']),
            'is_default' => intval($data['is_default']),
            'created_by' => get_current_user_id()
        );
        
        $result = $wpdb->insert($templates_table, $template_data);
        
        if ($result) {
            // If this is set as default, remove default from others
            if ($data['is_default']) {
                $wpdb->update(
                    $templates_table,
                    array('is_default' => 0),
                    array('id' => array('!=', $wpdb->insert_id)),
                    array('%d'),
                    array('%d')
                );
            }
            
            return $wpdb->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update template
     */
    public function update_template($template_id, $data) {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'spga_templates';
        
        $template_data = array(
            'name' => sanitize_text_field($data['name']),
            'description' => sanitize_textarea_field($data['description']),
            'content' => wp_kses_post($data['content']),
            'variables' => json_encode($data['variables']),
            'post_type' => sanitize_text_field($data['post_type']),
            'category_id' => intval($data['category_id']),
            'tags' => sanitize_text_field($data['tags']),
            'is_default' => intval($data['is_default'])
        );
        
        $result = $wpdb->update(
            $templates_table,
            $template_data,
            array('id' => $template_id),
            array('%s', '%s', '%s', '%s', '%s', '%d', '%s', '%d'),
            array('%d')
        );
        
        if ($result !== false) {
            // If this is set as default, remove default from others
            if ($data['is_default']) {
                $wpdb->update(
                    $templates_table,
                    array('is_default' => 0),
                    array('id' => array('!=', $template_id)),
                    array('%d'),
                    array('%d')
                );
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Delete template
     */
    public function delete_template($template_id) {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'spga_templates';
        
        // Soft delete by setting is_active to 0
        return $wpdb->update(
            $templates_table,
            array('is_active' => 0),
            array('id' => $template_id),
            array('%d'),
            array('%d')
        );
    }
    
    /**
     * Get available template variables
     */
    public function get_template_variables() {
        return $this->template_variables;
    }
    
    /**
     * Extract shortcodes from template content
     */
    public function extract_shortcodes($content) {
        $shortcodes = array();
        
        // Extract spga_dynamic shortcodes
        preg_match_all('/\[spga_dynamic[^\]]*name=["\']([^"\']+)["\'][^\]]*\]/', $content, $matches);
        if (!empty($matches[1])) {
            foreach ($matches[1] as $name) {
                $shortcodes[] = array(
                    'type' => 'dynamic',
                    'name' => $name
                );
            }
        }
        
        // Extract legacy dsc_dynamic shortcodes
        preg_match_all('/\[dsc_dynamic[^\]]*name=["\']([^"\']+)["\'][^\]]*\]/', $content, $matches);
        if (!empty($matches[1])) {
            foreach ($matches[1] as $name) {
                $shortcodes[] = array(
                    'type' => 'legacy',
                    'name' => $name
                );
            }
        }
        
        return $shortcodes;
    }
}
