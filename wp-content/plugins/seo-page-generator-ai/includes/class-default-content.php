<?php
/**
 * Default Content and Templates for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_Default_Content {
    
    /**
     * Create default template
     */
    public static function create_default_template() {
        $template_content = self::get_default_template_content();
        
        $template_data = array(
            'name' => 'Default SEO Template',
            'description' => 'A comprehensive template for SEO-optimized pages with dynamic content sections.',
            'content' => $template_content,
            'variables' => array(),
            'post_type' => 'page',
            'category_id' => 0,
            'tags' => 'seo, generated content',
            'is_default' => 1
        );
        
        return spga()->template_engine->create_template($template_data);
    }
    
    /**
     * Get default template content
     */
    public static function get_default_template_content() {
        return '<h1>[spga_dynamic name="h1" default="[title]"]</h1>

<div class="intro-section">
    [spga_dynamic name="h1_description" default="Professional [keyword] services in [location]. Get expert help and advice from our qualified team."]
</div>

<h2>[spga_dynamic name="h2" default="How We Can Help You"]</h2>

<div class="content-section">
    [spga_dynamic name="h2_introduction" default="Our comprehensive [keyword] services in [location] are designed to provide you with the support and guidance you need. We understand that every situation is unique, and we tailor our approach to meet your specific requirements."]
</div>

<h3>[spga_dynamic name="h3" default="Our Services"]</h3>

<div class="services-section">
    [spga_dynamic name="services_content" default="We offer a wide range of [keyword] services including:
    
    <ul>
    <li>Initial consultation and assessment</li>
    <li>Personalized action plans</li>
    <li>Ongoing support and guidance</li>
    <li>Expert advice from qualified professionals</li>
    </ul>
    
    Our team in [location] has years of experience helping people with [keyword] and we are committed to providing you with the best possible service."]
</div>

<h3>[spga_dynamic name="h3_2" default="Why Choose Us"]</h3>

<div class="benefits-section">
    [spga_dynamic name="benefits_content" default="When you choose our [keyword] services in [location], you benefit from:
    
    <ul>
    <li>Free initial consultation</li>
    <li>No obligation assessment</li>
    <li>Confidential and professional service</li>
    <li>Local expertise in [location]</li>
    <li>Proven track record of success</li>
    </ul>"]
</div>

<h3>[spga_dynamic name="h3_3" default="Get Started Today"]</h3>

<div class="cta-section">
    [spga_dynamic name="cta_content" default="Don\'t wait any longer to get the [keyword] help you need in [location]. Contact us today for your free, no-obligation consultation. Our friendly team is ready to discuss your situation and explain how we can help you move forward.
    
    <p><strong>Call us now or fill out our online form to get started.</strong></p>"]
</div>

<div class="contact-info">
    <h4>Contact Information</h4>
    <p>Serving [location] and surrounding areas</p>
    <p>Free consultation available</p>
</div>';
    }
    
    /**
     * Create default shortcodes
     */
    public static function create_default_shortcodes() {
        $shortcodes = array(
            array(
                'name' => 'h1',
                'description' => 'Main page heading (H1)',
                'prompt_template' => 'Generate a compelling H1 heading for a page about [TITLEOFPAGE]. Make it SEO-friendly and under 60 characters.',
                'word_count' => 10,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 1
            ),
            array(
                'name' => 'h1_description',
                'description' => 'Introduction paragraph under H1',
                'prompt_template' => 'Write a compelling introduction paragraph for [TITLEOFPAGE]. Focus on the benefits and what the user can expect. Keep it under 150 words.',
                'word_count' => 100,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 2
            ),
            array(
                'name' => 'h2',
                'description' => 'Secondary heading (H2)',
                'prompt_template' => 'Generate an engaging H2 subheading for a section about [TITLEOFPAGE]. Make it descriptive and relevant.',
                'word_count' => 8,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 3
            ),
            array(
                'name' => 'h2_introduction',
                'description' => 'Content under H2 heading',
                'prompt_template' => 'Write informative content for the H2 section about [TITLEOFPAGE]. Explain the services or benefits in detail. Include relevant keywords naturally.',
                'word_count' => 150,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 4
            ),
            array(
                'name' => 'h3',
                'description' => 'Third-level heading (H3)',
                'prompt_template' => 'Create a relevant H3 subheading for [TITLEOFPAGE] that focuses on services or features.',
                'word_count' => 6,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 5
            ),
            array(
                'name' => 'services_content',
                'description' => 'Services or features content',
                'prompt_template' => 'Write detailed content about the services or features related to [TITLEOFPAGE]. Include a bulleted list of key services and explain the benefits.',
                'word_count' => 200,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 6
            ),
            array(
                'name' => 'h3_2',
                'description' => 'Second H3 heading',
                'prompt_template' => 'Generate another H3 heading for [TITLEOFPAGE] that focuses on benefits or why choose us.',
                'word_count' => 6,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 7
            ),
            array(
                'name' => 'benefits_content',
                'description' => 'Benefits or advantages content',
                'prompt_template' => 'Write compelling content about the benefits and advantages of choosing services related to [TITLEOFPAGE]. Include a bulleted list of key benefits.',
                'word_count' => 150,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 8
            ),
            array(
                'name' => 'h3_3',
                'description' => 'Call-to-action heading',
                'prompt_template' => 'Create an action-oriented H3 heading for [TITLEOFPAGE] that encourages users to take the next step.',
                'word_count' => 6,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 9
            ),
            array(
                'name' => 'cta_content',
                'description' => 'Call-to-action content',
                'prompt_template' => 'Write persuasive call-to-action content for [TITLEOFPAGE]. Encourage users to contact or take action. Include urgency and benefits.',
                'word_count' => 100,
                'ai_provider' => 'gemini',
                'is_active' => 1,
                'sort_order' => 10
            )
        );
        
        global $wpdb;
        $shortcodes_table = $wpdb->prefix . 'spga_shortcodes';
        
        foreach ($shortcodes as $shortcode) {
            $wpdb->insert($shortcodes_table, $shortcode);
        }
        
        return count($shortcodes);
    }
    
    /**
     * Create default linking rules
     */
    public static function create_default_linking_rules() {
        $keywords = array(
            'Debt Help', 'Debt Advice', 'Write Off Debt', 'Help With Debt',
            'Free Debt Advice', 'Free Debt Help', 'Debt Management',
            'Debt Management Plan', 'Debt Management Plans', 'Write Off Debts',
            'Get Out Of Debt', 'IVA', 'Debt Company', 'Debt Management Company',
            'IVA Company', 'Debt Solutions', 'Write Off My Debt', 'Clear Debt',
            'Clear Debts', 'Debt Free', 'Debt Consolidation', 'Debt Relief', 'Manage Debt'
        );
        
        $created = 0;
        foreach ($keywords as $keyword) {
            $rule_data = array(
                'keyword' => $keyword,
                'target_url' => '',
                'target_post_id' => 0,
                'link_text' => $keyword,
                'max_links_per_page' => 3,
                'is_active' => 1,
                'priority' => 1
            );
            
            if (spga()->internal_linker->create_linking_rule($rule_data)) {
                $created++;
            }
        }
        
        return $created;
    }
    
    /**
     * Create sample keywords and locations
     */
    public static function create_sample_keywords() {
        $keywords = array('Debt Help', 'Debt Advice', 'Write Off Debt', 'Help With Debt', 'Free Debt Advice');
        $locations = array('London', 'Manchester', 'Birmingham', 'Liverpool', 'Leeds');
        
        global $wpdb;
        $keywords_table = $wpdb->prefix . 'spga_keywords';
        
        $created = 0;
        foreach ($keywords as $keyword) {
            foreach ($locations as $location) {
                $data = array(
                    'keyword' => $keyword,
                    'location' => $location,
                    'search_volume' => 0,
                    'competition' => 'unknown',
                    'priority' => 0,
                    'is_active' => 1,
                    'usage_count' => 0
                );
                
                if ($wpdb->insert($keywords_table, $data)) {
                    $created++;
                }
            }
        }
        
        return $created;
    }
    
    /**
     * Initialize all default content
     */
    public static function initialize_defaults() {
        $results = array();
        
        // Create default template
        $results['template'] = self::create_default_template();
        
        // Create default shortcodes
        $results['shortcodes'] = self::create_default_shortcodes();
        
        // Create default linking rules
        $results['linking_rules'] = self::create_default_linking_rules();
        
        // Create sample keywords
        $results['keywords'] = self::create_sample_keywords();
        
        return $results;
    }
}
