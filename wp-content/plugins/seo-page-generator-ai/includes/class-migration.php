<?php
/**
 * Migration utilities for SEO Page Generator AI
 * Handles importing data from existing plugins
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_Migration {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_spga_migrate_data', array($this, 'handle_migration_request'));
    }
    
    /**
     * Check if migration is needed
     */
    public function needs_migration() {
        $migrations_needed = array();
        
        // Check for Dynamic SEO Content Generator
        if ($this->has_dynamic_seo_data()) {
            $migrations_needed['dynamic_seo'] = 'Dynamic SEO Content Generator';
        }
        
        // Check for Auto Focus Keyword data
        if ($this->has_auto_focus_data()) {
            $migrations_needed['auto_focus'] = 'Auto Focus Keyword for SEO';
        }
        
        // Check for Smart Internal Linker data
        if ($this->has_smart_linker_data()) {
            $migrations_needed['smart_linker'] = 'Smart Internal Linker';
        }
        
        return $migrations_needed;
    }
    
    /**
     * Migrate all available data
     */
    public function migrate_all_data() {
        $results = array();
        
        // Migrate Dynamic SEO data
        if ($this->has_dynamic_seo_data()) {
            $results['dynamic_seo'] = $this->migrate_dynamic_seo_data();
        }
        
        // Migrate Auto Focus data
        if ($this->has_auto_focus_data()) {
            $results['auto_focus'] = $this->migrate_auto_focus_data();
        }
        
        // Migrate Smart Linker data
        if ($this->has_smart_linker_data()) {
            $results['smart_linker'] = $this->migrate_smart_linker_data();
        }
        
        return $results;
    }
    
    /**
     * Check for Dynamic SEO Content Generator data
     */
    private function has_dynamic_seo_data() {
        global $wpdb;
        
        // Check for options
        $options = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->options WHERE option_name LIKE 'dsc_%'");
        
        // Check for template file
        $template_file = WP_CONTENT_DIR . '/plugins/dynamic-seo-content-generator-updated-4/assets/template.txt';
        
        return $options > 0 || file_exists($template_file);
    }
    
    /**
     * Check for Auto Focus Keyword data
     */
    private function has_auto_focus_data() {
        global $wpdb;
        
        return $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->options WHERE option_name LIKE 'afkw_%'") > 0;
    }
    
    /**
     * Check for Smart Internal Linker data
     */
    private function has_smart_linker_data() {
        return get_option('sil_keywords') !== false;
    }
    
    /**
     * Migrate Dynamic SEO Content Generator data
     */
    public function migrate_dynamic_seo_data() {
        $results = array(
            'templates' => 0,
            'shortcodes' => 0,
            'keywords' => 0,
            'settings' => 0
        );
        
        try {
            // Migrate template
            $template_file = WP_CONTENT_DIR . '/plugins/dynamic-seo-content-generator-updated-4/assets/template.txt';
            if (file_exists($template_file)) {
                $template_content = file_get_contents($template_file);
                
                if ($template_content) {
                    $template_id = spga()->template_engine->create_template(array(
                        'name' => 'Migrated Dynamic SEO Template',
                        'description' => 'Template migrated from Dynamic SEO Content Generator',
                        'content' => $template_content,
                        'variables' => array(),
                        'post_type' => 'page',
                        'category_id' => 0,
                        'tags' => '',
                        'is_default' => 1
                    ));
                    
                    if ($template_id) {
                        $results['templates'] = 1;
                    }
                }
            }
            
            // Migrate shortcodes (descriptions)
            global $wpdb;
            $shortcode_options = $wpdb->get_results("SELECT option_name, option_value FROM $wpdb->options WHERE option_name LIKE 'dsc_desc_%'");
            
            foreach ($shortcode_options as $option) {
                $name = str_replace('dsc_desc_', '', $option->option_name);
                $data = json_decode($option->option_value, true);
                
                if ($data) {
                    $shortcode_data = array(
                        'name' => $name,
                        'description' => $data['description'] ?? '',
                        'prompt_template' => $data['description'] ?? '',
                        'word_count' => intval($data['word_count'] ?? 100),
                        'ai_provider' => 'gemini',
                        'is_active' => 1,
                        'sort_order' => $results['shortcodes'] + 1
                    );
                    
                    $this->create_shortcode($shortcode_data);
                    $results['shortcodes']++;
                }
            }
            
            // Migrate keywords and locations
            $keywords = get_option('dsc_h1_keywords', array());
            $locations = get_option('dsc_locations', array());
            
            if (!empty($keywords) && !empty($locations)) {
                foreach ($keywords as $keyword) {
                    foreach ($locations as $location) {
                        $this->create_keyword_location($keyword, $location);
                        $results['keywords']++;
                    }
                }
            }
            
            // Migrate settings
            $gemini_key = get_option('dsc_gemini_api_key');
            $openai_key = get_option('dsc_openai_api_key');
            
            if ($gemini_key) {
                update_option('spga_gemini_api_key', $gemini_key);
                $results['settings']++;
            }
            
            if ($openai_key) {
                update_option('spga_openai_api_key', $openai_key);
                $results['settings']++;
            }
            
        } catch (Exception $e) {
            error_log('SPGA Migration Error (Dynamic SEO): ' . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Migrate Auto Focus Keyword data
     */
    public function migrate_auto_focus_data() {
        $results = array(
            'settings' => 0,
            'post_types' => 0
        );
        
        try {
            // Migrate settings
            $afkw_settings = get_option('afkw_auto-focus-keyword-for-seo', array());
            
            if (!empty($afkw_settings)) {
                if (isset($afkw_settings['post_types'])) {
                    // Store supported post types
                    update_option('spga_supported_post_types', $afkw_settings['post_types']);
                    $results['post_types'] = count($afkw_settings['post_types']);
                }
                
                $results['settings'] = 1;
            }
            
        } catch (Exception $e) {
            error_log('SPGA Migration Error (Auto Focus): ' . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Migrate Smart Internal Linker data
     */
    public function migrate_smart_linker_data() {
        $results = array(
            'linking_rules' => 0,
            'settings' => 0
        );
        
        try {
            // Migrate keywords
            $keywords = get_option('sil_keywords', array());
            
            if (!empty($keywords)) {
                foreach ($keywords as $keyword) {
                    $rule_data = array(
                        'keyword' => $keyword,
                        'target_url' => '',
                        'target_post_id' => 0,
                        'link_text' => $keyword,
                        'max_links_per_page' => 3,
                        'is_active' => 1,
                        'priority' => 1
                    );
                    
                    spga()->internal_linker->create_linking_rule($rule_data);
                    $results['linking_rules']++;
                }
            }
            
            // Migrate OpenAI key if available
            $openai_key = get_option('sil_openai_key');
            if ($openai_key && !get_option('spga_openai_api_key')) {
                update_option('spga_openai_api_key', $openai_key);
                $results['settings']++;
            }
            
        } catch (Exception $e) {
            error_log('SPGA Migration Error (Smart Linker): ' . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Create shortcode in database
     */
    private function create_shortcode($data) {
        global $wpdb;
        $shortcodes_table = $wpdb->prefix . 'spga_shortcodes';
        
        return $wpdb->insert($shortcodes_table, $data);
    }
    
    /**
     * Create keyword-location combination
     */
    private function create_keyword_location($keyword, $location) {
        global $wpdb;
        $keywords_table = $wpdb->prefix . 'spga_keywords';
        
        $data = array(
            'keyword' => sanitize_text_field($keyword),
            'location' => sanitize_text_field($location),
            'search_volume' => 0,
            'competition' => 'unknown',
            'priority' => 0,
            'is_active' => 1,
            'usage_count' => 0
        );
        
        return $wpdb->insert($keywords_table, $data);
    }
    
    /**
     * Import titles.json from Dynamic SEO
     */
    public function import_titles_json() {
        $titles_file = WP_CONTENT_DIR . '/plugins/dynamic-seo-content-generator-updated-4/titles.json';
        
        if (!file_exists($titles_file)) {
            return false;
        }
        
        $titles_data = json_decode(file_get_contents($titles_file), true);
        
        if (!$titles_data) {
            return false;
        }
        
        $imported = 0;
        
        foreach ($titles_data as $title => $data) {
            // Parse keyword and location from title
            $parts = explode(' ', $title);
            if (count($parts) >= 2) {
                $location = array_pop($parts);
                $keyword = implode(' ', $parts);
                
                // Add to queue if not already processed
                if ($data['status'] === 'pending') {
                    $queue_items = array(
                        array(
                            'keyword' => $keyword,
                            'location' => $location,
                            'custom_prompt' => '',
                            'template_id' => 1, // Default template
                            'post_type' => strtolower($data['type'] ?? 'post'),
                            'priority' => 0
                        )
                    );
                    
                    $added = spga()->progress_tracker->add_to_queue($queue_items);
                    $imported += $added;
                }
            }
        }
        
        return $imported;
    }
    
    /**
     * Clean up old plugin data (optional)
     */
    public function cleanup_old_data($plugin = 'all') {
        $cleaned = array();
        
        if ($plugin === 'all' || $plugin === 'dynamic_seo') {
            $cleaned['dynamic_seo'] = $this->cleanup_dynamic_seo_data();
        }
        
        if ($plugin === 'all' || $plugin === 'auto_focus') {
            $cleaned['auto_focus'] = $this->cleanup_auto_focus_data();
        }
        
        if ($plugin === 'all' || $plugin === 'smart_linker') {
            $cleaned['smart_linker'] = $this->cleanup_smart_linker_data();
        }
        
        return $cleaned;
    }
    
    /**
     * Cleanup Dynamic SEO data
     */
    private function cleanup_dynamic_seo_data() {
        global $wpdb;
        
        $deleted = $wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE 'dsc_%'");
        
        // Remove files (be careful with this)
        $files_to_remove = array(
            WP_CONTENT_DIR . '/plugins/dynamic-seo-content-generator-updated-4/titles.json',
            WP_CONTENT_DIR . '/plugins/dynamic-seo-content-generator-updated-4/cron.log',
            WP_CONTENT_DIR . '/plugins/dynamic-seo-content-generator-updated-4/cron_result.txt',
            WP_CONTENT_DIR . '/plugins/dynamic-seo-content-generator-updated-4/response.txt'
        );
        
        $files_deleted = 0;
        foreach ($files_to_remove as $file) {
            if (file_exists($file) && unlink($file)) {
                $files_deleted++;
            }
        }
        
        return array('options' => $deleted, 'files' => $files_deleted);
    }
    
    /**
     * Cleanup Auto Focus data
     */
    private function cleanup_auto_focus_data() {
        global $wpdb;
        
        $deleted = $wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE 'afkw_%'");
        
        return array('options' => $deleted);
    }
    
    /**
     * Cleanup Smart Linker data
     */
    private function cleanup_smart_linker_data() {
        $options_deleted = 0;
        
        $options_to_remove = array('sil_keywords', 'sil_openai_key');
        
        foreach ($options_to_remove as $option) {
            if (delete_option($option)) {
                $options_deleted++;
            }
        }
        
        return array('options' => $options_deleted);
    }
    
    /**
     * AJAX handler for migration requests
     */
    public function handle_migration_request() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $action = sanitize_text_field($_POST['migration_action'] ?? '');
        
        switch ($action) {
            case 'check_migration':
                $needed = $this->needs_migration();
                wp_send_json_success($needed);
                break;
                
            case 'migrate_all':
                $results = $this->migrate_all_data();
                wp_send_json_success($results);
                break;
                
            case 'import_titles':
                $imported = $this->import_titles_json();
                wp_send_json_success(array('imported' => $imported));
                break;
                
            case 'cleanup':
                $plugin = sanitize_text_field($_POST['plugin'] ?? 'all');
                $cleaned = $this->cleanup_old_data($plugin);
                wp_send_json_success($cleaned);
                break;
                
            default:
                wp_send_json_error('Invalid migration action');
        }
    }
    
    /**
     * Get migration status
     */
    public function get_migration_status() {
        return array(
            'needs_migration' => $this->needs_migration(),
            'migration_completed' => get_option('spga_migration_completed', false),
            'migration_date' => get_option('spga_migration_date', '')
        );
    }
    
    /**
     * Mark migration as completed
     */
    public function mark_migration_completed() {
        update_option('spga_migration_completed', true);
        update_option('spga_migration_date', current_time('mysql'));
    }
}
