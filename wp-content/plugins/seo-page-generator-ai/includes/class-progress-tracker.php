<?php
/**
 * Progress Tracker for SEO Page Generator AI
 * Handles queue processing and progress tracking
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_Progress_Tracker {
    
    /**
     * Constructor
     */
    public function __construct() {
        // AJAX handlers for progress tracking
        add_action('wp_ajax_spga_start_processing', array($this, 'start_processing'));
        add_action('wp_ajax_spga_get_progress_status', array($this, 'get_progress_status'));
        add_action('wp_ajax_spga_pause_processing', array($this, 'pause_processing'));
        add_action('wp_ajax_spga_resume_processing', array($this, 'resume_processing'));
        add_action('wp_ajax_spga_stop_processing', array($this, 'stop_processing'));
        add_action('wp_ajax_spga_retry_failed', array($this, 'retry_failed_items'));
    }
    
    /**
     * Process the content generation queue
     */
    public function process_queue() {
        // Check if processing is paused
        if (get_option('spga_queue_paused', false)) {
            return;
        }
        
        // Prevent multiple simultaneous processing
        $lock_key = 'spga_processing_lock';
        $lock_timeout = 300; // 5 minutes
        
        if (get_transient($lock_key)) {
            return;
        }
        
        set_transient($lock_key, time(), $lock_timeout);
        
        try {
            $this->process_batch();
        } finally {
            delete_transient($lock_key);
        }
    }
    
    /**
     * Process a batch of queue items
     */
    private function process_batch() {
        $batch_size = get_option('spga_batch_size', 5);
        $pending_items = $this->get_pending_items($batch_size);
        
        if (empty($pending_items)) {
            return;
        }
        
        foreach ($pending_items as $item) {
            $this->process_queue_item($item);
            
            // Add delay between items
            $delay = get_option('spga_processing_delay', 60);
            if ($delay > 0) {
                sleep($delay);
            }
        }
    }
    
    /**
     * Process a single queue item
     */
    public function process_queue_item($item) {
        // Update status to processing
        $this->update_queue_status($item->id, 'processing');

        // Log processing start
        $this->log_activity($item->id, 'queue_processing', 'info', 'Started processing queue item');

        try {
            // Generate title
            $title = $this->generate_title($item->keyword, $item->location);
            
            // Get template
            $template = spga()->template_engine->get_template($item->template_id);
            if (!$template) {
                throw new Exception('Template not found');
            }
            
            // Get shortcodes from template
            $shortcodes = $this->get_template_shortcodes($template->content);
            
            // Generate AI content for sections
            $ai_content = array();
            if (!empty($shortcodes)) {
                $ai_content = spga()->ai_content->generate_sections($shortcodes, $title);
            }
            
            // Process template with data
            $template_data = array(
                'keyword' => $item->keyword,
                'location' => $item->location,
                'title' => $title
            );
            
            spga()->template_engine->set_generated_content($ai_content);
            $content = spga()->template_engine->process_template($template->content, $template_data);
            
            // Generate meta data
            $meta_title = $item->meta_title ?: spga()->ai_content->generate_meta_title($item->keyword, $item->location);
            $meta_description = $item->meta_description ?: spga()->ai_content->generate_meta_description($item->keyword, $item->location);
            $focus_keyword = $item->focus_keyword ?: $item->keyword;
            
            // Create the post
            $post_data = array(
                'post_title' => $title,
                'post_content' => $content,
                'post_status' => $this->get_publish_status($item),
                'post_type' => $item->post_type,
                'post_author' => get_current_user_id(),
                'meta_input' => array(
                    '_spga_generated' => 1,
                    '_spga_queue_id' => $item->id,
                    '_spga_keyword' => $item->keyword,
                    '_spga_location' => $item->location
                )
            );
            
            // Add category if specified
            if (!empty($template->category_id) && $item->post_type === 'post') {
                $post_data['post_category'] = array($template->category_id);
            }
            
            // Add tags if specified
            if (!empty($template->tags)) {
                $post_data['tags_input'] = explode(',', $template->tags);
            }
            
            $post_id = wp_insert_post($post_data);
            
            if (is_wp_error($post_id)) {
                throw new Exception('Failed to create post: ' . $post_id->get_error_message());
            }
            
            // Update queue with post ID and meta data
            $this->update_queue_item($item->id, array(
                'post_id' => $post_id,
                'meta_title' => $meta_title,
                'meta_description' => $meta_description,
                'focus_keyword' => $focus_keyword,
                'ai_content_generated' => 1,
                'status' => 'completed',
                'processed_at' => current_time('mysql')
            ));
            
            // SEO optimization will be handled by the SEO optimizer class hooks
            // Internal linking will be handled by the internal linker class hooks
            
            $this->log_activity($item->id, 'content_generated', 'success', "Successfully generated content for '{$title}'");
            
        } catch (Exception $e) {
            $this->update_queue_status($item->id, 'failed', $e->getMessage());
            $this->log_activity($item->id, 'content_generation_failed', 'error', $e->getMessage());
        }
    }
    
    /**
     * Generate title from keyword and location
     */
    private function generate_title($keyword, $location) {
        return trim($keyword . ' ' . $location);
    }
    
    /**
     * Get template shortcodes
     */
    private function get_template_shortcodes($template_content) {
        global $wpdb;
        $shortcodes_table = $wpdb->prefix . 'spga_shortcodes';
        
        // Extract shortcode names from template
        $shortcode_names = array();
        preg_match_all('/\[(?:spga_dynamic|dsc_dynamic)[^\]]*name=["\']([^"\']+)["\'][^\]]*\]/', $template_content, $matches);
        
        if (!empty($matches[1])) {
            $shortcode_names = array_unique($matches[1]);
        }
        
        if (empty($shortcode_names)) {
            return array();
        }
        
        // Get shortcode definitions from database
        $placeholders = implode(',', array_fill(0, count($shortcode_names), '%s'));
        $query = "SELECT * FROM $shortcodes_table WHERE name IN ($placeholders) AND is_active = 1";
        
        return $wpdb->get_results($wpdb->prepare($query, $shortcode_names));
    }
    
    /**
     * Get publish status for item
     */
    private function get_publish_status($item) {
        if (!empty($item->scheduled_at)) {
            return 'future';
        }
        
        $auto_publish = get_option('spga_auto_publish', false);
        return $auto_publish ? 'publish' : 'draft';
    }
    
    /**
     * Get pending queue items
     */
    private function get_pending_items($limit = 5) {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $queue_table 
             WHERE status = 'pending' 
             AND (scheduled_at IS NULL OR scheduled_at <= NOW())
             ORDER BY priority DESC, created_at ASC 
             LIMIT %d",
            $limit
        ));
    }
    
    /**
     * Add items to queue
     */
    public function add_to_queue($items) {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $added_count = 0;
        
        foreach ($items as $item) {
            $queue_data = array(
                'keyword' => sanitize_text_field($item['keyword']),
                'location' => sanitize_text_field($item['location']),
                'custom_prompt' => sanitize_textarea_field($item['custom_prompt'] ?? ''),
                'template_id' => intval($item['template_id']),
                'post_type' => sanitize_text_field($item['post_type'] ?? 'post'),
                'status' => 'pending',
                'priority' => intval($item['priority'] ?? 0),
                'scheduled_at' => !empty($item['scheduled_at']) ? $item['scheduled_at'] : null
            );
            
            $result = $wpdb->insert($queue_table, $queue_data);
            
            if ($result) {
                $added_count++;
                $this->log_activity($wpdb->insert_id, 'item_added_to_queue', 'success', "Added '{$item['keyword']} {$item['location']}' to queue");
            }
        }
        
        return $added_count;
    }
    
    /**
     * Update queue item status
     */
    public function update_queue_status($item_id, $status, $error_message = '') {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $update_data = array('status' => $status);
        
        if (!empty($error_message)) {
            $update_data['error_message'] = $error_message;
        }
        
        if ($status === 'completed') {
            $update_data['processed_at'] = current_time('mysql');
        }
        
        return $wpdb->update(
            $queue_table,
            $update_data,
            array('id' => $item_id),
            array('%s', '%s', '%s'),
            array('%d')
        );
    }
    
    /**
     * Update queue item with multiple fields
     */
    public function update_queue_item($item_id, $data) {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        return $wpdb->update(
            $queue_table,
            $data,
            array('id' => $item_id)
        );
    }
    
    /**
     * Get queue statistics
     */
    public function get_queue_statistics() {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        return $wpdb->get_row("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = 'paused' THEN 1 ELSE 0 END) as paused
            FROM $queue_table
        ");
    }
    
    /**
     * AJAX: Start processing
     */
    public function start_processing() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Resume processing if paused
        update_option('spga_queue_paused', false);
        
        // Trigger immediate processing
        do_action('spga_process_queue');
        
        wp_send_json_success('Processing started');
    }
    
    /**
     * AJAX: Get progress status
     */
    public function get_progress_status() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $stats = $this->get_queue_statistics();
        $is_paused = get_option('spga_queue_paused', false);
        
        $response = array(
            'stats' => $stats,
            'is_paused' => $is_paused,
            'progress_percentage' => $stats->total > 0 ? round(($stats->completed / $stats->total) * 100, 2) : 0
        );
        
        wp_send_json_success($response);
    }
    
    /**
     * AJAX: Pause processing
     */
    public function pause_processing() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        update_option('spga_queue_paused', true);
        wp_send_json_success('Processing paused');
    }
    
    /**
     * AJAX: Resume processing
     */
    public function resume_processing() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        update_option('spga_queue_paused', false);
        wp_send_json_success('Processing resumed');
    }
    
    /**
     * AJAX: Stop processing
     */
    public function stop_processing() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        update_option('spga_queue_paused', true);
        
        // Reset processing items to pending
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $wpdb->update(
            $queue_table,
            array('status' => 'pending'),
            array('status' => 'processing'),
            array('%s'),
            array('%s')
        );
        
        wp_send_json_success('Processing stopped');
    }
    
    /**
     * AJAX: Retry failed items
     */
    public function retry_failed_items() {
        check_ajax_referer('spga_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $result = $wpdb->update(
            $queue_table,
            array('status' => 'pending', 'error_message' => ''),
            array('status' => 'failed'),
            array('%s', '%s'),
            array('%s')
        );
        
        wp_send_json_success("Reset {$result} failed items for retry");
    }
    
    /**
     * Log activity
     */
    private function log_activity($queue_id, $action, $status, $message, $details = array()) {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'spga_logs';
        
        $wpdb->insert(
            $logs_table,
            array(
                'queue_id' => $queue_id,
                'action' => $action,
                'status' => $status,
                'message' => $message,
                'details' => json_encode($details),
                'user_id' => get_current_user_id(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0',
                'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? '', 0, 500)
            ),
            array('%d', '%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );
    }
}
