<?php
/**
 * Database management class for SEO Page Generator AI
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_Database {
    
    /**
     * Create all required database tables
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Content generation queue table
        $queue_table = $wpdb->prefix . 'spga_queue';
        $queue_sql = "CREATE TABLE $queue_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            keyword varchar(255) NOT NULL,
            location varchar(255) NOT NULL,
            custom_prompt text,
            template_id bigint(20) unsigned,
            post_type varchar(20) DEFAULT 'post',
            status enum('pending','processing','completed','failed','paused') DEFAULT 'pending',
            post_id bigint(20) unsigned NULL,
            meta_title varchar(255),
            meta_description text,
            focus_keyword varchar(255),
            internal_links_added int(11) DEFAULT 0,
            ai_content_generated tinyint(1) DEFAULT 0,
            seo_optimized tinyint(1) DEFAULT 0,
            error_message text,
            priority int(11) DEFAULT 0,
            scheduled_at datetime NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            processed_at datetime NULL,
            PRIMARY KEY (id),
            KEY status (status),
            KEY keyword (keyword),
            KEY location (location),
            KEY template_id (template_id),
            KEY created_at (created_at),
            KEY scheduled_at (scheduled_at)
        ) $charset_collate;";
        
        // Template management table
        $templates_table = $wpdb->prefix . 'spga_templates';
        $templates_sql = "CREATE TABLE $templates_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            content longtext NOT NULL,
            variables longtext,
            post_type varchar(20) DEFAULT 'post',
            category_id bigint(20) unsigned NULL,
            tags text,
            is_default tinyint(1) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            usage_count int(11) DEFAULT 0,
            created_by bigint(20) unsigned,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY name (name),
            KEY post_type (post_type),
            KEY is_default (is_default),
            KEY is_active (is_active),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        // Progress and activity logs table
        $logs_table = $wpdb->prefix . 'spga_logs';
        $logs_sql = "CREATE TABLE $logs_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            queue_id bigint(20) unsigned,
            action varchar(100) NOT NULL,
            status enum('info','success','warning','error') DEFAULT 'info',
            message text,
            details json,
            user_id bigint(20) unsigned NULL,
            ip_address varchar(45),
            user_agent text,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY queue_id (queue_id),
            KEY action (action),
            KEY status (status),
            KEY timestamp (timestamp),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        // Shortcode definitions table (migrated from Dynamic SEO)
        $shortcodes_table = $wpdb->prefix . 'spga_shortcodes';
        $shortcodes_sql = "CREATE TABLE $shortcodes_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            description text,
            prompt_template text,
            word_count int(11) DEFAULT 0,
            ai_provider enum('gemini','openai','custom') DEFAULT 'gemini',
            is_active tinyint(1) DEFAULT 1,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY name (name),
            KEY is_active (is_active),
            KEY sort_order (sort_order)
        ) $charset_collate;";
        
        // Keywords and locations management
        $keywords_table = $wpdb->prefix . 'spga_keywords';
        $keywords_sql = "CREATE TABLE $keywords_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            keyword varchar(255) NOT NULL,
            location varchar(255) NOT NULL,
            search_volume int(11) DEFAULT 0,
            competition varchar(20) DEFAULT 'unknown',
            priority int(11) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            usage_count int(11) DEFAULT 0,
            last_used datetime NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY keyword_location (keyword, location),
            KEY keyword (keyword),
            KEY location (location),
            KEY is_active (is_active),
            KEY priority (priority)
        ) $charset_collate;";
        
        // Internal linking rules
        $linking_table = $wpdb->prefix . 'spga_linking_rules';
        $linking_sql = "CREATE TABLE $linking_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            keyword varchar(255) NOT NULL,
            target_url varchar(500),
            target_post_id bigint(20) unsigned NULL,
            link_text varchar(255),
            max_links_per_page int(11) DEFAULT 3,
            is_active tinyint(1) DEFAULT 1,
            priority int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY keyword (keyword),
            KEY target_post_id (target_post_id),
            KEY is_active (is_active),
            KEY priority (priority)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        // Execute table creation
        dbDelta($queue_sql);
        dbDelta($templates_sql);
        dbDelta($logs_sql);
        dbDelta($shortcodes_sql);
        dbDelta($keywords_sql);
        dbDelta($linking_sql);
        
        // Insert default data
        self::insert_default_data();
        
        // Log table creation
        error_log('SPGA: Database tables created successfully');
    }
    
    /**
     * Upgrade database tables
     */
    public static function upgrade_tables() {
        // For future version upgrades
        self::create_tables();
    }
    
    /**
     * Insert default data
     */
    private static function insert_default_data() {
        global $wpdb;
        
        // Insert default template
        $templates_table = $wpdb->prefix . 'spga_templates';
        $default_template_exists = $wpdb->get_var(
            "SELECT COUNT(*) FROM $templates_table WHERE is_default = 1"
        );
        
        if (!$default_template_exists) {
            $default_template_content = self::get_default_template_content();
            
            $wpdb->insert(
                $templates_table,
                array(
                    'name' => 'Default SEO Template',
                    'description' => 'Default template with SEO optimization and internal linking',
                    'content' => $default_template_content,
                    'variables' => json_encode(array(
                        'keyword' => 'Primary keyword',
                        'location' => 'Target location',
                        'h1' => 'Main heading',
                        'h2' => 'Secondary heading',
                        'h1_description' => 'H1 description content',
                        'h2_introduction' => 'H2 introduction content'
                    )),
                    'post_type' => 'page',
                    'is_default' => 1,
                    'is_active' => 1,
                    'created_by' => get_current_user_id()
                ),
                array('%s', '%s', '%s', '%s', '%s', '%d', '%d', '%d', '%d')
            );
        }
        
        // Insert default shortcodes
        $shortcodes_table = $wpdb->prefix . 'spga_shortcodes';
        $default_shortcodes = array(
            array(
                'name' => 'h1',
                'description' => 'Main page heading',
                'prompt_template' => 'Generate an SEO-optimized H1 heading for [TITLEOFPAGE]',
                'word_count' => 10
            ),
            array(
                'name' => 'h1_description',
                'description' => 'Description under H1',
                'prompt_template' => 'Write a compelling description for [TITLEOFPAGE] that encourages action',
                'word_count' => 50
            ),
            array(
                'name' => 'h2',
                'description' => 'Secondary heading',
                'prompt_template' => 'Create an H2 heading related to [TITLEOFPAGE]',
                'word_count' => 15
            ),
            array(
                'name' => 'h2_introduction',
                'description' => 'Introduction content',
                'prompt_template' => 'Write an introduction paragraph about [TITLEOFPAGE]',
                'word_count' => 100
            )
        );
        
        foreach ($default_shortcodes as $index => $shortcode) {
            $exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM $shortcodes_table WHERE name = %s",
                    $shortcode['name']
                )
            );
            
            if (!$exists) {
                $wpdb->insert(
                    $shortcodes_table,
                    array(
                        'name' => $shortcode['name'],
                        'description' => $shortcode['description'],
                        'prompt_template' => $shortcode['prompt_template'],
                        'word_count' => $shortcode['word_count'],
                        'sort_order' => $index + 1
                    ),
                    array('%s', '%s', '%s', '%d', '%d')
                );
            }
        }
        
        // Insert default linking rules
        $linking_table = $wpdb->prefix . 'spga_linking_rules';
        $default_keywords = array(
            'Debt Help', 'Debt Advice', 'Write Off Debt', 'Help With Debt',
            'Free Debt Advice', 'Free Debt Help', 'Debt Management',
            'Debt Management Plan', 'IVA', 'Debt Solutions'
        );
        
        foreach ($default_keywords as $keyword) {
            $exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM $linking_table WHERE keyword = %s",
                    $keyword
                )
            );
            
            if (!$exists) {
                $wpdb->insert(
                    $linking_table,
                    array(
                        'keyword' => $keyword,
                        'link_text' => $keyword,
                        'max_links_per_page' => 3,
                        'is_active' => 1,
                        'priority' => 1
                    ),
                    array('%s', '%s', '%d', '%d', '%d')
                );
            }
        }
    }
    
    /**
     * Get default template content
     */
    private static function get_default_template_content() {
        return '<div class="seo-content-wrapper">
    <header class="hero-section">
        <h1>[spga_dynamic name="h1"]</h1>
        <p class="hero-description">[spga_dynamic name="h1_description"]</p>
        <div class="cta-section">
            <a href="#qualify" class="btn btn-primary">Check if you qualify</a>
            <p class="security-note">Safe, secure & confidential</p>
        </div>
    </header>
    
    <section class="main-content">
        <h2>[spga_dynamic name="h2"]</h2>
        <div class="content-body">
            [spga_dynamic name="h2_introduction"]
        </div>
        <a href="#qualify" class="btn btn-secondary">Get Started</a>
    </section>
    
    <section class="process-section">
        <h2>How do I get started?</h2>
        <div class="process-steps">
            <div class="step">
                <h3>Answer a few quick questions</h3>
                <p>Use our easy online questionnaire to start the process.</p>
            </div>
            <div class="step">
                <h3>Speak to a specialist</h3>
                <p>Our friendly, experienced team will explain all available options.</p>
            </div>
            <div class="step">
                <h3>Choose your plan</h3>
                <p>Select the best solution for your circumstances and lifestyle.</p>
            </div>
        </div>
    </section>
    
    <section class="qualification-form" id="qualify">
        <h2>Check if you qualify</h2>
        <!-- Form content will be inserted here -->
    </section>
</div>';
    }
    
    /**
     * Drop all plugin tables (for uninstall)
     */
    public static function drop_tables() {
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . 'spga_queue',
            $wpdb->prefix . 'spga_templates',
            $wpdb->prefix . 'spga_logs',
            $wpdb->prefix . 'spga_shortcodes',
            $wpdb->prefix . 'spga_keywords',
            $wpdb->prefix . 'spga_linking_rules'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
        
        error_log('SPGA: Database tables dropped');
    }
}
