<?php
/**
 * SEO Optimizer for SEO Page Generator AI
 * Handles automatic focus keyword assignment and SEO optimization
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_SEO_Optimizer {
    
    /**
     * Supported SEO plugins
     */
    private $supported_plugins = array(
        'yoast' => 'wordpress-seo/wp-seo.php',
        'rankmath' => 'seo-by-rankmath/rank-math.php'
    );
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('save_post', array($this, 'auto_optimize_post'), 20, 2);
        add_action('wp_insert_post', array($this, 'set_focus_keyword'), 10, 3);
    }
    
    /**
     * Auto-optimize post on save
     */
    public function auto_optimize_post($post_id, $post) {
        // Skip if not enabled
        if (!get_option('spga_seo_optimization', true)) {
            return;
        }
        
        // Skip revisions and autosaves
        if (wp_is_post_revision($post_id) || wp_is_post_autosave($post_id)) {
            return;
        }
        
        // Check if this is a generated post
        $is_generated = get_post_meta($post_id, '_spga_generated', true);
        if (!$is_generated) {
            return;
        }
        
        $this->optimize_post($post_id);
    }
    
    /**
     * Optimize a specific post
     */
    public function optimize_post($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        // Get queue item data
        $queue_data = $this->get_queue_data_for_post($post_id);
        
        if ($queue_data) {
            // Set focus keyword
            $this->set_post_focus_keyword($post_id, $queue_data->focus_keyword);
            
            // Set meta title and description
            if (!empty($queue_data->meta_title)) {
                $this->set_meta_title($post_id, $queue_data->meta_title);
            }
            
            if (!empty($queue_data->meta_description)) {
                $this->set_meta_description($post_id, $queue_data->meta_description);
            }
            
            // Update optimization status
            $this->update_queue_seo_status($queue_data->id, true);
            
            return true;
        }
        
        // Fallback: extract keyword from title
        $keyword = $this->extract_keyword_from_title($post->post_title);
        if ($keyword) {
            $this->set_post_focus_keyword($post_id, $keyword);
            return true;
        }
        
        return false;
    }
    
    /**
     * Set focus keyword for post
     */
    public function set_focus_keyword($post_id, $post, $update) {
        // Skip if not enabled
        if (!get_option('spga_seo_optimization', true)) {
            return;
        }
        
        // Only process on insert (not update)
        if ($update) {
            return;
        }
        
        // Check if this is a generated post
        $is_generated = get_post_meta($post_id, '_spga_generated', true);
        if (!$is_generated) {
            return;
        }
        
        // Get keyword from queue data
        $queue_data = $this->get_queue_data_for_post($post_id);
        if ($queue_data && !empty($queue_data->focus_keyword)) {
            $this->set_post_focus_keyword($post_id, $queue_data->focus_keyword);
        }
    }
    
    /**
     * Set focus keyword for specific post
     */
    public function set_post_focus_keyword($post_id, $keyword) {
        $active_plugin = $this->get_active_seo_plugin();
        
        switch ($active_plugin) {
            case 'yoast':
                $this->set_yoast_focus_keyword($post_id, $keyword);
                break;
            case 'rankmath':
                $this->set_rankmath_focus_keyword($post_id, $keyword);
                break;
            default:
                // Store in custom meta as fallback
                update_post_meta($post_id, '_spga_focus_keyword', sanitize_text_field($keyword));
                break;
        }
        
        $this->log_activity('focus_keyword_set', 'success', "Focus keyword '{$keyword}' set for post {$post_id}");
    }
    
    /**
     * Set Yoast SEO focus keyword
     */
    private function set_yoast_focus_keyword($post_id, $keyword) {
        update_post_meta($post_id, '_yoast_wpseo_focuskw', sanitize_text_field($keyword));
        
        // Also set primary category if it's a post
        $post = get_post($post_id);
        if ($post && $post->post_type === 'post') {
            $categories = wp_get_post_categories($post_id);
            if (!empty($categories)) {
                update_post_meta($post_id, '_yoast_wpseo_primary_category', $categories[0]);
            }
        }
    }
    
    /**
     * Set RankMath focus keyword
     */
    private function set_rankmath_focus_keyword($post_id, $keyword) {
        update_post_meta($post_id, 'rank_math_focus_keyword', sanitize_text_field($keyword));
        
        // Set additional RankMath meta
        update_post_meta($post_id, 'rank_math_seo_score', 80); // Default good score
    }
    
    /**
     * Set meta title
     */
    public function set_meta_title($post_id, $title) {
        $active_plugin = $this->get_active_seo_plugin();
        
        switch ($active_plugin) {
            case 'yoast':
                update_post_meta($post_id, '_yoast_wpseo_title', sanitize_text_field($title));
                break;
            case 'rankmath':
                update_post_meta($post_id, 'rank_math_title', sanitize_text_field($title));
                break;
            default:
                update_post_meta($post_id, '_spga_meta_title', sanitize_text_field($title));
                break;
        }
    }
    
    /**
     * Set meta description
     */
    public function set_meta_description($post_id, $description) {
        $active_plugin = $this->get_active_seo_plugin();
        
        switch ($active_plugin) {
            case 'yoast':
                update_post_meta($post_id, '_yoast_wpseo_metadesc', sanitize_textarea_field($description));
                break;
            case 'rankmath':
                update_post_meta($post_id, 'rank_math_description', sanitize_textarea_field($description));
                break;
            default:
                update_post_meta($post_id, '_spga_meta_description', sanitize_textarea_field($description));
                break;
        }
    }
    
    /**
     * Bulk optimize posts
     */
    public function bulk_optimize($post_ids, $options = array()) {
        $results = array();
        
        foreach ($post_ids as $post_id) {
            $result = $this->optimize_post($post_id);
            $results[$post_id] = $result;
            
            // Add delay to prevent overwhelming the server
            if (!empty($options['delay'])) {
                sleep(intval($options['delay']));
            }
        }
        
        return $results;
    }
    
    /**
     * Get active SEO plugin
     */
    public function get_active_seo_plugin() {
        foreach ($this->supported_plugins as $plugin => $file) {
            if (is_plugin_active($file)) {
                return $plugin;
            }
        }
        
        return false;
    }
    
    /**
     * Check if SEO plugin is available
     */
    public function is_seo_plugin_active() {
        return $this->get_active_seo_plugin() !== false;
    }
    
    /**
     * Get SEO plugin capabilities
     */
    public function get_seo_capabilities() {
        $active_plugin = $this->get_active_seo_plugin();
        
        $capabilities = array(
            'focus_keyword' => false,
            'meta_title' => false,
            'meta_description' => false,
            'schema' => false,
            'sitemap' => false
        );
        
        if ($active_plugin) {
            $capabilities = array(
                'focus_keyword' => true,
                'meta_title' => true,
                'meta_description' => true,
                'schema' => true,
                'sitemap' => true
            );
        }
        
        return $capabilities;
    }
    
    /**
     * Extract keyword from post title
     */
    private function extract_keyword_from_title($title) {
        // Remove common stop words and extract main keyword
        $stop_words = array('the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should');
        
        $words = explode(' ', strtolower($title));
        $keywords = array();
        
        foreach ($words as $word) {
            $word = trim($word, '.,!?;:"()[]{}');
            if (strlen($word) > 2 && !in_array($word, $stop_words)) {
                $keywords[] = $word;
            }
        }
        
        // Return first 2-3 meaningful words as keyword phrase
        return implode(' ', array_slice($keywords, 0, 3));
    }
    
    /**
     * Get queue data for post
     */
    private function get_queue_data_for_post($post_id) {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $queue_table WHERE post_id = %d",
            $post_id
        ));
    }
    
    /**
     * Update queue SEO optimization status
     */
    private function update_queue_seo_status($queue_id, $status) {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $wpdb->update(
            $queue_table,
            array('seo_optimized' => $status ? 1 : 0),
            array('id' => $queue_id),
            array('%d'),
            array('%d')
        );
    }
    
    /**
     * Generate SEO-friendly slug
     */
    public function generate_seo_slug($title, $keyword = '') {
        $slug = sanitize_title($title);
        
        // If keyword is provided, try to include it in slug
        if (!empty($keyword) && strpos($slug, sanitize_title($keyword)) === false) {
            $keyword_slug = sanitize_title($keyword);
            $slug = $keyword_slug . '-' . $slug;
        }
        
        return $slug;
    }
    
    /**
     * Analyze content for SEO
     */
    public function analyze_content($content, $keyword = '') {
        $analysis = array(
            'word_count' => str_word_count(strip_tags($content)),
            'keyword_density' => 0,
            'keyword_count' => 0,
            'readability_score' => 0,
            'has_headings' => false,
            'has_images' => false,
            'recommendations' => array()
        );
        
        if (!empty($keyword)) {
            $keyword_count = substr_count(strtolower($content), strtolower($keyword));
            $analysis['keyword_count'] = $keyword_count;
            $analysis['keyword_density'] = $analysis['word_count'] > 0 ? ($keyword_count / $analysis['word_count']) * 100 : 0;
            
            // Add recommendations based on keyword density
            if ($analysis['keyword_density'] < 0.5) {
                $analysis['recommendations'][] = 'Consider adding the focus keyword more frequently (current density: ' . round($analysis['keyword_density'], 2) . '%)';
            } elseif ($analysis['keyword_density'] > 3) {
                $analysis['recommendations'][] = 'Keyword density is too high (current: ' . round($analysis['keyword_density'], 2) . '%). Consider reducing keyword usage.';
            }
        }
        
        // Check for headings
        if (preg_match('/<h[1-6]/', $content)) {
            $analysis['has_headings'] = true;
        } else {
            $analysis['recommendations'][] = 'Add headings (H1, H2, H3) to improve content structure';
        }
        
        // Check for images
        if (preg_match('/<img/', $content)) {
            $analysis['has_images'] = true;
        } else {
            $analysis['recommendations'][] = 'Consider adding relevant images to improve user engagement';
        }
        
        // Word count recommendations
        if ($analysis['word_count'] < 300) {
            $analysis['recommendations'][] = 'Content is quite short. Consider expanding to at least 300 words for better SEO.';
        }
        
        return $analysis;
    }
    
    /**
     * Get SEO statistics
     */
    public function get_seo_statistics() {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $stats = $wpdb->get_row("
            SELECT 
                COUNT(*) as total_posts,
                SUM(CASE WHEN seo_optimized = 1 THEN 1 ELSE 0 END) as optimized_posts,
                SUM(CASE WHEN focus_keyword != '' THEN 1 ELSE 0 END) as posts_with_keywords,
                SUM(CASE WHEN meta_title != '' THEN 1 ELSE 0 END) as posts_with_meta_titles,
                SUM(CASE WHEN meta_description != '' THEN 1 ELSE 0 END) as posts_with_meta_descriptions
            FROM $queue_table 
            WHERE status = 'completed'
        ");
        
        return $stats;
    }
    
    /**
     * Log activity
     */
    private function log_activity($action, $status, $message, $details = array()) {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'spga_logs';
        
        $wpdb->insert(
            $logs_table,
            array(
                'action' => $action,
                'status' => $status,
                'message' => $message,
                'details' => json_encode($details),
                'user_id' => get_current_user_id(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0',
                'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? '', 0, 500)
            ),
            array('%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );
    }
}
