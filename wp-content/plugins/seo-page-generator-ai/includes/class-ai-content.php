<?php
/**
 * AI Content Generation for SEO Page Generator AI
 * Handles Google Gemini and OpenAI API integration
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_AI_Content {
    
    /**
     * API endpoints
     */
    private $gemini_endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    private $openai_endpoint = 'https://api.openai.com/v1/chat/completions';
    
    /**
     * Constructor
     */
    public function __construct() {
        // No hooks needed for this class
    }
    
    /**
     * Generate content using AI
     */
    public function generate_content($prompt, $options = array()) {
        $defaults = array(
            'provider' => get_option('spga_ai_provider', 'gemini'),
            'word_count' => 100,
            'temperature' => 0.7,
            'max_tokens' => 3000
        );
        
        $options = wp_parse_args($options, $defaults);
        
        // Try primary provider first
        $result = $this->call_ai_provider($options['provider'], $prompt, $options);
        
        // If primary fails, try fallback
        if (!$result && $options['provider'] === 'gemini') {
            $result = $this->call_ai_provider('openai', $prompt, $options);
        } elseif (!$result && $options['provider'] === 'openai') {
            $result = $this->call_ai_provider('gemini', $prompt, $options);
        }
        
        return $result;
    }
    
    /**
     * Generate content for multiple sections
     */
    public function generate_sections($sections, $title, $options = array()) {
        if (empty($sections)) {
            return false;
        }
        
        // Build combined prompt
        $prompts = array();
        $section_info = "";
        
        foreach ($sections as $section) {
            $description = str_replace('[TITLEOFPAGE]', $title, $section['prompt_template']);
            $word_count = intval($section['word_count']);
            
            $prompts[] = "Section '{$section['name']}': {$description}. Limit to {$word_count} words. ||";
            $section_info .= "Section '{$section['name']}':\n content here \n || \n";
        }
        
        $combined_prompt = "Generate content for the following sections. Separate each section with '||' and label with Section 'name':\n" . 
                          implode("\n", $prompts) . 
                          "\n I need all these sections in response: " . $section_info . 
                          "\n Don't add any heading in sections, USE HTML FORMATTING IN RESPONSE SO I WILL DIRECTLY POST CONTENT TO WEBSITE";
        
        $response = $this->generate_content($combined_prompt, $options);
        
        if ($response) {
            return $this->parse_sections_response($response);
        }
        
        return false;
    }
    
    /**
     * Parse AI response into sections
     */
    private function parse_sections_response($response) {
        $sections = array();
        $parts = explode("||", trim($response));
        
        foreach ($parts as $part) {
            if (preg_match("/Section '([^']+)':\s*(.+)/s", trim($part), $matches)) {
                $name = $matches[1];
                $content = trim($matches[2]);
                $sections[$name] = $content;
            }
        }
        
        return $sections;
    }
    
    /**
     * Call specific AI provider
     */
    private function call_ai_provider($provider, $prompt, $options) {
        switch ($provider) {
            case 'gemini':
                return $this->call_gemini_api($prompt, $options);
            case 'openai':
                return $this->call_openai_api($prompt, $options);
            default:
                return false;
        }
    }
    
    /**
     * Call Google Gemini API
     */
    private function call_gemini_api($prompt, $options) {
        $api_key = get_option('spga_gemini_api_key');
        
        if (empty($api_key)) {
            $this->log_error('Gemini API key not configured');
            return false;
        }
        
        $url = $this->gemini_endpoint . '?key=' . $api_key;
        
        $data = array(
            'contents' => array(
                array(
                    'parts' => array(
                        array('text' => $prompt)
                    )
                )
            ),
            'generationConfig' => array(
                'temperature' => floatval($options['temperature']),
                'maxOutputTokens' => intval($options['max_tokens'])
            )
        );
        
        $response = wp_remote_post($url, array(
            'headers' => array(
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 120
        ));
        
        if (is_wp_error($response)) {
            $this->log_error('Gemini API request failed: ' . $response->get_error_message());
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);
        
        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return $result['candidates'][0]['content']['parts'][0]['text'];
        }
        
        $this->log_error('Gemini API response error: ' . $body);
        return false;
    }
    
    /**
     * Call OpenAI API
     */
    private function call_openai_api($prompt, $options) {
        $api_key = get_option('spga_openai_api_key');
        
        if (empty($api_key)) {
            $this->log_error('OpenAI API key not configured');
            return false;
        }
        
        $data = array(
            'model' => 'gpt-3.5-turbo',
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'temperature' => floatval($options['temperature']),
            'max_tokens' => intval($options['max_tokens'])
        );
        
        $response = wp_remote_post($this->openai_endpoint, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 120
        ));
        
        if (is_wp_error($response)) {
            $this->log_error('OpenAI API request failed: ' . $response->get_error_message());
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);
        
        if (isset($result['choices'][0]['message']['content'])) {
            return $result['choices'][0]['message']['content'];
        }
        
        $this->log_error('OpenAI API response error: ' . $body);
        return false;
    }
    
    /**
     * Generate meta title
     */
    public function generate_meta_title($keyword, $location, $options = array()) {
        $prompt = "Generate an SEO-optimized meta title (under 60 characters) for a page about '{$keyword}' in '{$location}'. Make it compelling and include the main keyword.";
        
        return $this->generate_content($prompt, array_merge($options, array('word_count' => 15)));
    }
    
    /**
     * Generate meta description
     */
    public function generate_meta_description($keyword, $location, $options = array()) {
        $prompt = "Generate an SEO-optimized meta description (under 160 characters) for a page about '{$keyword}' in '{$location}'. Make it compelling and include a call-to-action.";
        
        return $this->generate_content($prompt, array_merge($options, array('word_count' => 30)));
    }
    
    /**
     * Test API connection
     */
    public function test_api_connection($provider = null) {
        if (!$provider) {
            $provider = get_option('spga_ai_provider', 'gemini');
        }
        
        $test_prompt = "Generate a simple test response with the word 'success'.";
        $result = $this->call_ai_provider($provider, $test_prompt, array('word_count' => 10));
        
        return !empty($result);
    }
    
    /**
     * Get API usage statistics
     */
    public function get_api_usage() {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'spga_logs';
        
        $stats = $wpdb->get_row("
            SELECT 
                COUNT(*) as total_requests,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_requests,
                SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as failed_requests,
                SUM(CASE WHEN action = 'ai_generation' AND status = 'success' THEN 1 ELSE 0 END) as content_generated
            FROM $logs_table 
            WHERE action LIKE '%ai%' 
            AND timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        
        return $stats;
    }
    
    /**
     * Log error message
     */
    private function log_error($message) {
        error_log('SPGA AI Content: ' . $message);
        
        // Also log to database
        $this->log_activity('ai_generation', 'error', $message);
    }
    
    /**
     * Log activity to database
     */
    private function log_activity($action, $status, $message, $details = array()) {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'spga_logs';
        
        $wpdb->insert(
            $logs_table,
            array(
                'action' => $action,
                'status' => $status,
                'message' => $message,
                'details' => json_encode($details),
                'user_id' => get_current_user_id(),
                'ip_address' => $this->get_client_ip(),
                'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? '', 0, 500)
            ),
            array('%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Clean and format AI response
     */
    public function clean_response($content) {
        // Remove unwanted characters and formatting
        $content = trim($content);
        
        // Remove markdown formatting if present
        $content = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $content);
        $content = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $content);
        
        // Ensure proper paragraph formatting
        $content = wpautop($content);
        
        return $content;
    }
    
    /**
     * Estimate token count for pricing
     */
    public function estimate_tokens($text) {
        // Rough estimation: 1 token ≈ 4 characters
        return ceil(strlen($text) / 4);
    }
    
    /**
     * Get rate limits for providers
     */
    public function get_rate_limits() {
        return array(
            'gemini' => array(
                'requests_per_minute' => 60,
                'tokens_per_minute' => 32000
            ),
            'openai' => array(
                'requests_per_minute' => 20,
                'tokens_per_minute' => 40000
            )
        );
    }
}
