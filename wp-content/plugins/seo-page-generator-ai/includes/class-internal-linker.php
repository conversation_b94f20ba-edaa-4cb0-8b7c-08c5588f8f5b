<?php
/**
 * Internal Linker for SEO Page Generator AI
 * Handles automatic internal linking with configurable keywords
 */

if (!defined('ABSPATH')) {
    exit;
}

class SPGA_Internal_Linker {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_insert_post', array($this, 'auto_add_internal_links'), 15, 3);
        add_filter('the_content', array($this, 'add_dynamic_internal_links'), 10);
    }
    
    /**
     * Auto-add internal links when post is created
     */
    public function auto_add_internal_links($post_id, $post, $update) {
        // Skip if not enabled
        if (!get_option('spga_internal_linking', true)) {
            return;
        }
        
        // Only process on insert (not update)
        if ($update) {
            return;
        }
        
        // Check if this is a generated post
        $is_generated = get_post_meta($post_id, '_spga_generated', true);
        if (!$is_generated) {
            return;
        }
        
        $this->process_post_links($post_id);
    }
    
    /**
     * Process internal links for a specific post
     */
    public function process_post_links($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        $original_content = $post->post_content;
        $updated_content = $this->add_internal_links($original_content, $post_id);
        
        if ($updated_content !== $original_content) {
            // Update post content
            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $updated_content
            ));
            
            // Update queue status
            $this->update_queue_linking_status($post_id, true);
            
            // Count links added
            $links_added = $this->count_links_difference($original_content, $updated_content);
            $this->update_queue_links_count($post_id, $links_added);
            
            $this->log_activity('internal_links_added', 'success', "Added {$links_added} internal links to post {$post_id}");
            
            return $links_added;
        }
        
        return 0;
    }
    
    /**
     * Add internal links to content
     */
    public function add_internal_links($content, $exclude_post_id = 0) {
        $linking_rules = $this->get_linking_rules();
        $links_added = 0;
        $max_links = 5; // Maximum links per post
        
        foreach ($linking_rules as $rule) {
            if ($links_added >= $max_links) {
                break;
            }
            
            $keyword = $rule->keyword;
            $max_links_for_keyword = min($rule->max_links_per_page, $max_links - $links_added);
            
            // Find keyword occurrences
            $pattern = '/\b' . preg_quote($keyword, '/') . '\b/i';
            $matches = array();
            preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
            
            if (!empty($matches[0])) {
                $target_url = $this->get_target_url($rule, $exclude_post_id);
                
                if ($target_url) {
                    $replacements = 0;
                    
                    // Process matches in reverse order to maintain offsets
                    $matches_to_process = array_reverse($matches[0]);
                    
                    foreach ($matches_to_process as $match) {
                        if ($replacements >= $max_links_for_keyword) {
                            break;
                        }
                        
                        $matched_text = $match[0];
                        $offset = $match[1];
                        
                        // Check if this occurrence is already within a link
                        if ($this->is_within_link($content, $offset)) {
                            continue;
                        }
                        
                        // Create the link
                        $link_text = !empty($rule->link_text) ? $rule->link_text : $matched_text;
                        $link = '<a href="' . esc_url($target_url) . '">' . esc_html($link_text) . '</a>';
                        
                        // Replace the text with the link
                        $content = substr_replace($content, $link, $offset, strlen($matched_text));
                        $replacements++;
                        $links_added++;
                    }
                }
            }
        }
        
        return $content;
    }
    
    /**
     * Add dynamic internal links (for existing content)
     */
    public function add_dynamic_internal_links($content) {
        // Only add dynamic links if enabled and not in admin
        if (!get_option('spga_internal_linking', true) || is_admin()) {
            return $content;
        }
        
        // Check if post already has generated links
        global $post;
        if ($post && get_post_meta($post->ID, '_spga_links_generated', true)) {
            return $content;
        }
        
        return $this->add_internal_links($content, $post ? $post->ID : 0);
    }
    
    /**
     * Get linking rules from database
     */
    private function get_linking_rules() {
        global $wpdb;
        $linking_table = $wpdb->prefix . 'spga_linking_rules';
        
        return $wpdb->get_results(
            "SELECT * FROM $linking_table WHERE is_active = 1 ORDER BY priority DESC, keyword ASC"
        );
    }
    
    /**
     * Get target URL for linking rule
     */
    private function get_target_url($rule, $exclude_post_id = 0) {
        // If specific URL is set, use it
        if (!empty($rule->target_url)) {
            return $rule->target_url;
        }
        
        // If specific post ID is set, use it
        if (!empty($rule->target_post_id)) {
            $target_post = get_post($rule->target_post_id);
            if ($target_post && $target_post->post_status === 'publish' && $target_post->ID !== $exclude_post_id) {
                return get_permalink($target_post->ID);
            }
        }
        
        // Find a random relevant post
        $args = array(
            'post_type' => array('post', 'page'),
            'post_status' => 'publish',
            'posts_per_page' => 1,
            'orderby' => 'rand',
            'exclude' => array($exclude_post_id),
            's' => $rule->keyword
        );
        
        $posts = get_posts($args);
        
        if (!empty($posts)) {
            return get_permalink($posts[0]->ID);
        }
        
        // Fallback: get any random post
        $args['s'] = '';
        $posts = get_posts($args);
        
        if (!empty($posts)) {
            return get_permalink($posts[0]->ID);
        }
        
        return false;
    }
    
    /**
     * Check if position is within an existing link
     */
    private function is_within_link($content, $offset) {
        // Find the nearest opening and closing link tags
        $before_content = substr($content, 0, $offset);
        $after_content = substr($content, $offset);
        
        $last_opening = strrpos($before_content, '<a ');
        $last_closing = strrpos($before_content, '</a>');
        $next_closing = strpos($after_content, '</a>');
        
        // If there's an opening tag after the last closing tag, and there's a closing tag after our position
        if ($last_opening !== false && ($last_closing === false || $last_opening > $last_closing) && $next_closing !== false) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Count difference in links between two content versions
     */
    private function count_links_difference($original, $updated) {
        $original_links = preg_match_all('/<a\s+[^>]*href=[^>]*>/i', $original);
        $updated_links = preg_match_all('/<a\s+[^>]*href=[^>]*>/i', $updated);
        
        return $updated_links - $original_links;
    }
    
    /**
     * Bulk process internal links
     */
    public function bulk_process_links($post_ids, $options = array()) {
        $results = array();
        
        foreach ($post_ids as $post_id) {
            $links_added = $this->process_post_links($post_id);
            $results[$post_id] = $links_added;
            
            // Add delay to prevent overwhelming the server
            if (!empty($options['delay'])) {
                sleep(intval($options['delay']));
            }
        }
        
        return $results;
    }
    
    /**
     * Create linking rule
     */
    public function create_linking_rule($data) {
        global $wpdb;
        $linking_table = $wpdb->prefix . 'spga_linking_rules';
        
        $rule_data = array(
            'keyword' => sanitize_text_field($data['keyword']),
            'target_url' => esc_url_raw($data['target_url']),
            'target_post_id' => intval($data['target_post_id']),
            'link_text' => sanitize_text_field($data['link_text']),
            'max_links_per_page' => intval($data['max_links_per_page']),
            'is_active' => intval($data['is_active']),
            'priority' => intval($data['priority'])
        );
        
        $result = $wpdb->insert($linking_table, $rule_data);
        
        if ($result) {
            return $wpdb->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update linking rule
     */
    public function update_linking_rule($rule_id, $data) {
        global $wpdb;
        $linking_table = $wpdb->prefix . 'spga_linking_rules';
        
        $rule_data = array(
            'keyword' => sanitize_text_field($data['keyword']),
            'target_url' => esc_url_raw($data['target_url']),
            'target_post_id' => intval($data['target_post_id']),
            'link_text' => sanitize_text_field($data['link_text']),
            'max_links_per_page' => intval($data['max_links_per_page']),
            'is_active' => intval($data['is_active']),
            'priority' => intval($data['priority'])
        );
        
        return $wpdb->update(
            $linking_table,
            $rule_data,
            array('id' => $rule_id),
            array('%s', '%s', '%d', '%s', '%d', '%d', '%d'),
            array('%d')
        );
    }
    
    /**
     * Delete linking rule
     */
    public function delete_linking_rule($rule_id) {
        global $wpdb;
        $linking_table = $wpdb->prefix . 'spga_linking_rules';
        
        return $wpdb->delete(
            $linking_table,
            array('id' => $rule_id),
            array('%d')
        );
    }
    
    /**
     * Get all linking rules
     */
    public function get_all_linking_rules() {
        global $wpdb;
        $linking_table = $wpdb->prefix . 'spga_linking_rules';
        
        return $wpdb->get_results(
            "SELECT * FROM $linking_table ORDER BY priority DESC, keyword ASC"
        );
    }
    
    /**
     * Import default linking rules
     */
    public function import_default_rules() {
        $default_keywords = array(
            'Debt Help', 'Debt Advice', 'Write Off Debt', 'Help With Debt',
            'Free Debt Advice', 'Free Debt Help', 'Debt Management',
            'Debt Management Plan', 'Debt Management Plans', 'Write Off Debts',
            'Get Out Of Debt', 'IVA', 'Debt Company', 'Debt Management Company',
            'IVA Company', 'Debt Solutions', 'Write Off My Debt', 'Clear Debt',
            'Clear Debts', 'Debt Free', 'Debt Consolidation', 'Debt Relief', 'Manage Debt'
        );
        
        foreach ($default_keywords as $keyword) {
            $this->create_linking_rule(array(
                'keyword' => $keyword,
                'target_url' => '',
                'target_post_id' => 0,
                'link_text' => $keyword,
                'max_links_per_page' => 3,
                'is_active' => 1,
                'priority' => 1
            ));
        }
    }
    
    /**
     * Update queue linking status
     */
    private function update_queue_linking_status($post_id, $status) {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $wpdb->update(
            $queue_table,
            array('internal_links_added' => $status ? 1 : 0),
            array('post_id' => $post_id),
            array('%d'),
            array('%d')
        );
    }
    
    /**
     * Update queue links count
     */
    private function update_queue_links_count($post_id, $count) {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $wpdb->update(
            $queue_table,
            array('internal_links_added' => $count),
            array('post_id' => $post_id),
            array('%d'),
            array('%d')
        );
    }
    
    /**
     * Get linking statistics
     */
    public function get_linking_statistics() {
        global $wpdb;
        $queue_table = $wpdb->prefix . 'spga_queue';
        
        $stats = $wpdb->get_row("
            SELECT 
                COUNT(*) as total_posts,
                SUM(CASE WHEN internal_links_added > 0 THEN 1 ELSE 0 END) as posts_with_links,
                SUM(internal_links_added) as total_links_added,
                AVG(internal_links_added) as avg_links_per_post
            FROM $queue_table 
            WHERE status = 'completed'
        ");
        
        return $stats;
    }
    
    /**
     * Log activity
     */
    private function log_activity($action, $status, $message, $details = array()) {
        global $wpdb;
        $logs_table = $wpdb->prefix . 'spga_logs';
        
        $wpdb->insert(
            $logs_table,
            array(
                'action' => $action,
                'status' => $status,
                'message' => $message,
                'details' => json_encode($details),
                'user_id' => get_current_user_id(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0',
                'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? '', 0, 500)
            ),
            array('%s', '%s', '%s', '%s', '%d', '%s', '%s')
        );
    }
}
